import { useChangePasswordMutation } from "api";
import { BackBreadcrumb, PasswordField } from "components";
import { Form, Formik, FormikProps } from "formik";
import { ChangePasswordInitialValues } from "formSchema/initialValues";
import { ChangePasswordValidations } from "formSchema/schemaValidations";
import { <PERSON><PERSON>, Card, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { ChangePasswordInterface } from "types";
import "./styles.scss";

const ChangePassword = () => {
  const { mutateAsync: changePassword } = useChangePasswordMutation();

  const handleSubmit = async (
    values: ChangePasswordInterface,
    { setSubmitting, resetForm }: any,
  ) => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { confirmPassword, ...payload } = values;
    try {
      const response: any = await changePassword(payload);
      if (response?.success) {
        toast.success(response?.message);
        resetForm();
      }
    } catch (error: any) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <BackBreadcrumb />
      <div className="profile-section-form-container">
        <Container fluid>
          <Row className="justify-content-center align-items-center">
            <Col lg="9" xxl="7">
              <div className="auth-form w-100 m-0">
                <Card className="auth-form-card justify-content-center aligh-items-center">
                  <Card.Body className="d-flex gap-4 flex-column align-items-center justify-content-center">
                    <div className="d-flex flex-column" style={{ gap: "10px" }}>
                      <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
                        Change password
                      </h1>

                      <p className="mb-0 auth-form-description font-gray text-center">
                        Your new password must be different from previous
                        <br className="d-md-block d-none" />
                        used passwords.
                      </p>
                    </div>

                    <div className="website-form w-100">
                      <Formik
                        initialValues={ChangePasswordInitialValues}
                        validationSchema={ChangePasswordValidations}
                        onSubmit={handleSubmit}
                      >
                        {({
                          isSubmitting,
                        }: FormikProps<ChangePasswordInterface>) => (
                          <Form
                            className="d-flex flex-column"
                            style={{ gap: "30px" }}
                          >
                            <div className="form-group position-relative">
                              <PasswordField
                                label="Old Password"
                                fieldName="currentPassword"
                                placeholder="Enter old password"
                              />
                            </div>

                            <div className="form-group position-relative">
                              <PasswordField
                                label="Create New Password"
                                fieldName="newPassword"
                                placeholder="Create your new password"
                              />
                            </div>

                            <div className="form-group position-relative">
                              <PasswordField
                                label="Confirm Password"
                                fieldName="confirmPassword"
                                placeholder="Confirm your password"
                              />
                            </div>

                            <div
                              className="action-btns d-flex flex-column"
                              style={{ gap: "30px" }}
                            >
                              <Button
                                type="submit"
                                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                                disabled={isSubmitting}
                              >
                                {isSubmitting ? <Spinner /> : "Update Password"}
                              </Button>
                            </div>
                          </Form>
                        )}
                      </Formik>
                    </div>
                  </Card.Body>
                </Card>
              </div>
            </Col>
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default ChangePassword;
