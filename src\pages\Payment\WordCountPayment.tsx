import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { useConfigQuery, useGetWordCountPaymentDetails } from "api";
import { BackBreadcrumb } from "components";
import { HomeSuccess } from "pages/Home";
import { useEffect, useState } from "react";
import { Card, Col, Container, Row } from "react-bootstrap";
import { useSearchParams } from "react-router-dom";
import "./styles.scss";
import UpdateWordCount from "./UpdateWordCount";
import WordCountPaymentForm from "./WordCountPaymentForm";

export default function WordCountPayment() {
  const [stripePromise, setStripePromise] = useState<any>(null);

  const [searchParams] = useSearchParams();
  const word_limit_raw = searchParams.get("word_limit");
  const member_id_raw = searchParams.get("member_id");
  const parseParam = (param: string | null) => {
    if (!param || isNaN(Number(param))) return undefined;
    return Number(param);
  };

  const word_limit = parseParam(word_limit_raw);
  const member_id = parseParam(member_id_raw);

  const { data: { publishableKey = null } = {} }: any = useConfigQuery();

  const { data: paymentInfo = {}, isLoading: isPaymentInfoLoading } =
    useGetWordCountPaymentDetails({
      word_limit,
      member_id,
    });

  const { client_secret } = paymentInfo ?? {};

  useEffect(() => {
    if (publishableKey) {
      setStripePromise(loadStripe(publishableKey));
    }
  }, [publishableKey]);

  return (
    <>
      {isPaymentInfoLoading ? (
        <HomeSuccess type="PLEASE_WAIT" />
      ) : (
        <main className="payment-section d-flex bg-white flex-column align-items-stretch w-100 h-100">
          <BackBreadcrumb />
          <div className="payment-section-form-container">
            <Container fluid>
              <Row className="justify-content-center align-items-center">
                <Col lg="9" xxl="7">
                  <div className="auth-form w-100 m-0">
                    <Card className="auth-form-card justify-content-center align-items-center">
                      <Card.Body className="d-flex gap-4 flex-column justify-content-center">
                        {client_secret ? (
                          <WordCountPaymentForm
                            stripePromise={stripePromise}
                            clientSecret={client_secret}
                            intentInfo={paymentInfo}
                          />
                        ) : (
                          <Elements stripe={stripePromise}>
                            <UpdateWordCount
                              paymentInfo={paymentInfo}
                              member_id={member_id}
                            />
                          </Elements>
                        )}
                      </Card.Body>
                    </Card>
                  </div>
                </Col>
              </Row>
            </Container>
          </div>
        </main>
      )}
    </>
  );
}
