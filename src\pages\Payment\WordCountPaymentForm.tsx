import { Elements } from "@stripe/react-stripe-js";
import { CheckoutForm } from "components/WordCountPayment";

interface PaymentFormProps {
  stripePromise: any;
  clientSecret: string;
  intentInfo: any;
}

export default function WordCountPaymentForm({
  stripePromise,
  clientSecret,
  intentInfo,
}: PaymentFormProps) {
  return (
    <>
      <div className="d-flex flex-column" style={{ gap: "10px" }}>
        <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
          Payment Details
        </h1>
        <p className="mb-0 auth-form-description font-gray text-center">
          Enter your payment information below to complete your subscription.
        </p>
      </div>
      {clientSecret && stripePromise && (
        <Elements stripe={stripePromise} options={{ clientSecret }}>
          <CheckoutForm intentInfo={intentInfo} />
        </Elements>
      )}
    </>
  );
}
