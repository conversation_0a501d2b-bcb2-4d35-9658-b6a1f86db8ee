import { RiMailLine } from "@remixicon/react";
import { useLoginMutation } from "api";
import { Auth0Login, PasswordField } from "components";
import { LoginInitialValues } from "formSchema/initialValues";
import { LoginValidations } from "formSchema/schemaValidations";
import { ErrorMessage, Field, Form, Formik, FormikProps } from "formik";
import { SETTINGS } from "globals";
import { useEffect, useState } from "react";
import { But<PERSON>, Spinner } from "react-bootstrap";
import { Link } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setIsLoggingOut } from "stores";
import useUserStore, { setRememberMeInfo } from "stores/user";
import { LoginInterface } from "types";

const Login = () => {
  const login = useLoginMutation;

  const rememberMeInfo = useUserStore((state) => state.rememberMeInfo);

  const [rememberMe, setRememberMe] = useState<any>(
    rememberMeInfo?.email ?? false,
  );

  const handleSubmit = (values: LoginInterface, { setSubmitting }: any) => {
    try {
      login(values);
      setRememberMeInfo({ email: rememberMe ? values?.email : "" });
    } catch (error) {
      console.log(error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleRememberMe = (evt: React.ChangeEvent<HTMLInputElement>) => {
    const value = evt.target.checked;
    setRememberMe(value);
  };

  useEffect(() => {
    setIsLoggingOut(false);
  }, [setIsLoggingOut]);

  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column login-form̥"
      style={{ gap: "30px" }}
    >
      <div className="d-flex flex-column" style={{ gap: "23px" }}>
        <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
          Login
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          Enter your {SETTINGS.APP_NAME} Account details
        </p>
      </div>

      <div className="website-form">
        <Formik
          initialValues={{ ...LoginInitialValues, ...rememberMeInfo }}
          validationSchema={LoginValidations}
          onSubmit={handleSubmit}
        >
          {({ isSubmitting }: FormikProps<any>) => (
            <Form className="d-flex flex-column" style={{ gap: "30px" }}>
              <div className="form-group position-relative">
                <label className="form-label">Email</label>
                <Field
                  name="email"
                  className="form-control"
                  placeholder="Enter your email"
                  type="email"
                />
                <ErrorMessage component={"span"} name="email" />
                <RiMailLine size={"20px"} color="#70828D" />
              </div>

              <div className="form-group position-relative">
                <PasswordField
                  label="Password"
                  fieldName="password"
                  placeholder="Enter your password"
                />
              </div>

              <div className="interaction-btns d-flex justify-content-between align-items-center">
                <div className="form-check form-check-inline">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    id="formCheckbox"
                    onChange={handleRememberMe}
                    checked={rememberMe}
                  />
                  <label
                    className="form-check-label font-gray fw-500"
                    htmlFor="formCheckbox"
                  >
                    Remember Me
                  </label>
                </div>

                <Link
                  to={ROUTE_PATH.FORGOT}
                  className="mb-0 fw-bold text-decoration-none font-primary"
                >
                  Forgot password?
                </Link>
              </div>

              <div
                className="action-btns d-flex flex-column"
                style={{ gap: "30px" }}
              >
                <Button
                  type="submit"
                  className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? <Spinner /> : "Login Now"}
                </Button>
                <Auth0Login />
              </div>

              <div className="interaction-btns d-flex align-items-center font-gray">
                Don't have an account?
                <Link
                  to={ROUTE_PATH.SIGNUP}
                  className="mb-0 fw-bold text-decoration-none ms-1"
                  style={{ color: "#60A799" }}
                >
                  Register Now
                </Link>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </div>
  );
};

export default Login;
