import { useStripe } from "@stripe/react-stripe-js";
import { useCreatePaymentMutation, useUpdateUpcomingPlanMutation } from "api";
import { SETTINGS } from "globals";
import { useState } from "react";
import { isMobileOnly } from "react-device-detect";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { getAppOriginURL, getStripeFormateDate } from "utils";
import PaymentBreakdownDetails from "./PaymentBreakdown";

const SUBSCRIPTION_UPDATE_INFO = {
  upgrade: {
    text: "You will be able to access your new benefits immediately. For the current period you will be charged for the difference in subscription price, on a prorata basis. You will be charged the new full amount at the start of your next subscription period.",
  },
  downgrade: {
    text: "You will be charged the new amount at the end of your current subscription period.",
  },
  tier: {
    text: "Looking to grow your team? Make the payment to add more licenses.",
  },
};

const UpdateSubscription = ({ planInfo, team_size }: any) => {
  const { mutateAsync: updatePlan } = useCreatePaymentMutation();
  const { mutateAsync: updateUpcomingPlan } = useUpdateUpcomingPlanMutation();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const stripe: any = useStripe();

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setLoading(true);
    try {
      const res: any = await updatePlan({
        subscription_id: planInfo?.subscription_id,
        proration_date: planInfo?.proration_date,
        team_size,
      });
      const { success, data } = res || {};
      if (success) {
        if (data?.is_upcoming_plan) {
          const result: any = await updateUpcomingPlan({
            payment_intent_id: data?.id,
          });
          if (result?.success) {
            navigate(
              `${ROUTE_PATH.PAYMENT_STATUS}?upcoming_plan_status=succeeded`,
            );
          }
        } else if (!["card"].includes(data?.payment_type)) {
          await stripe.confirmPayment({
            clientSecret: data?.client_secret,
            confirmParams: {
              return_url: `${getAppOriginURL()}${ROUTE_PATH.PAYMENT_STATUS}`,
            },
          });
        } else if (data?.payment_intent_status === "requires_action") {
          const { paymentIntent, error } = await stripe.handleNextAction({
            clientSecret: data?.client_secret,
          });

          if (error?.type) {
            toast.error(error.message, {
              id: error.message ?? "generic-error",
            });
            return;
          }

          if (!paymentIntent?.id) {
            return;
          }

          navigate(
            `${ROUTE_PATH.PAYMENT_STATUS}?upcoming_plan_status=${paymentIntent?.status}&payment_intent=${paymentIntent?.id}`,
          );
        } else {
          if (data?.is_paid && data?.payment_status === "paid") {
            navigate(`${ROUTE_PATH.PAYMENT_STATUS}?is_fully_paid=true`);
          } else {
            navigate(
              `${ROUTE_PATH.PAYMENT_STATUS}?payment_intent=${data?.id}`,
              {
                replace: true,
              },
            );
          }
        }
      }
    } catch (error: any) {
      console.log("An unexpected error occured.", error);
    } finally {
      setLoading(false);
    }
  };

  const getSubscriptionUpdateInfo = () => {
    switch (true) {
      case planInfo?.is_tier:
        return SUBSCRIPTION_UPDATE_INFO.tier.text;
      case planInfo?.is_upgraded:
        return SUBSCRIPTION_UPDATE_INFO.upgrade.text;
      case !planInfo?.is_upgraded:
        return SUBSCRIPTION_UPDATE_INFO.downgrade.text;
      default:
        return null;
    }
  };

  return (
    <>
      <div
        className={`d-flex flex-column ${isMobileOnly ? "text-center" : ""}`}
        style={{ gap: "10px" }}
      >
        <h1 className="mb-0 text-center auth-form-heading text-uppercase fw-bold">
          Payment Details
        </h1>

        <p className="mb-0 auth-form-description font-gray text-center">
          {getSubscriptionUpdateInfo()}
        </p>

        {planInfo?.payment_details && (
          <PaymentBreakdownDetails
            paymentItems={planInfo?.payment_details}
            planInfo={planInfo}
          />
        )}

        {!planInfo?.is_upgraded && (
          <div className="plan-details mt-3">
            <p className="plan-details-label mb-2">Subscription Type :</p>

            <p
              className={`mb-0 plan-details-value d-flex justify-content-start align-items-center ${isMobileOnly ? "justify-content-center p-0" : ""}`}
            >
              {planInfo?.title}
            </p>

            <div
              className="d-flex text-lg-start text-center flex-lg-row flex-column justify-content-center align-items-center mt-3"
              style={{ gap: "10px", fontSize: "18px" }}
            >
              <p className="mb-0">
                <strong className="me-1">Amount:</strong>
                <br className="d-lg-none d-block" />
                {SETTINGS.CURRENCY_INFO.GBP.symbol}
                {planInfo?.amount}
              </p>

              <p className="mb-0">
                <strong className="me-1">Validity:</strong>
                <br className="d-lg-none d-block" />
                {getStripeFormateDate(planInfo?.start_date)} -{" "}
                {getStripeFormateDate(planInfo?.expire_date)}
              </p>
            </div>
          </div>
        )}
      </div>

      <form
        id="payment-form"
        className="d-flex flex-column"
        style={{ gap: "20px" }}
        onSubmit={handleSubmit}
      >
        <div className="website-form w-100 mt-3">
          <div className="action-btns d-flex flex-column gap-3">
            <button
              id="submit"
              className="btn submit-btn w-100 bg-brown border-brown text-uppercase font-light"
              disabled={loading}
            >
              {loading ? "Please wait..." : "Update Plan"}
            </button>
          </div>
        </div>
      </form>
    </>
  );
};

export default UpdateSubscription;
