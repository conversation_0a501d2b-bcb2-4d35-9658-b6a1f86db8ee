import { RiArticleFill } from "@remixicon/react";
import {
  useGetDocumentSettings,
  useSaveDocumentSettings,
  useSaveOrgDocumentSettings,
} from "api";
import { BackBreadcrumb } from "components";
import { Formik, Form as FormikForm } from "formik";
import { DocSettingInitialValues } from "formSchema/initialValues";
import { DocSettingValidation } from "formSchema/schemaValidations";
import { Button, Col, Container, Row, Spinner } from "react-bootstrap";
import toast from "react-hot-toast";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { getOrganisationInfo, setDocSettings } from "stores";
import { convertObjToFormData } from "utils";
import FontSelection from "./FontSelection";
import LogoSelection from "./LogoSelection";
import PreviewArea from "./PreviewArea";
import "./styles.scss";
import { isMobileOnly } from "react-device-detect";

export default function DocSettings() {
  const { mutateAsync: saveDocumentSettings } = useSaveDocumentSettings();
  const { mutateAsync: saveOrgDocumentSettings } = useSaveOrgDocumentSettings();

  const [searchParams] = useSearchParams();
  const saveType = searchParams.get("type");
  const orgId = searchParams.get("orgId");
  const navigate = useNavigate();

  const { data: { export_settings = {} } = {}, isLoading } =
    useGetDocumentSettings({
      enabled: !saveType && !orgId,
      queryKey: ["export_settings", orgId],
    });
  const organisation = getOrganisationInfo();

  const savedDocSettings =
    saveType === "organisation" && orgId
      ? organisation?.export_settings
      : export_settings;

  const handleSubmit = async (values: any, { setSubmitting }: any) => {
    if (saveType === "organisation" && !orgId) {
      setDocSettings(values);
      toast.success("Settings saved successfully");
      navigate(-1);
      return;
    }
    try {
      setSubmitting(true);
      const payload = convertObjToFormData(values);
      const result: any =
        saveType === "organisation" && orgId
          ? await saveOrgDocumentSettings({ payload, orgId })
          : await saveDocumentSettings(payload);
      if (result?.success) {
        toast.success(result?.message);
        if (saveType === "organisation" && orgId) {
          navigate(ROUTE_PATH.ORGANISATIONAL_PROFILE);
        }
      }
    } catch (error) {
      console.error("Error saving settings:", error);
    } finally {
      setSubmitting(false);
    }
  };

  const handleValidation = async (
    values: any,
    { setErrors, validateForm, setSubmitting }: any,
  ) => {
    const validationErrors = await validateForm();
    setErrors(validationErrors);

    if (Object.keys(validationErrors).length > 0) {
      toast.error("Please fill all required fields before saving.");
    } else {
      handleSubmit(values, { setSubmitting });
    }
  };

  return (
    <Formik
      initialValues={{ ...DocSettingInitialValues, ...savedDocSettings }}
      enableReinitialize
      validationSchema={DocSettingValidation}
      onSubmit={handleSubmit}
    >
      {({
        values,
        setFieldValue,
        isSubmitting,
        validateForm,
        setErrors,
        setSubmitting,
      }) => (
        <FormikForm>
          <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
            <BackBreadcrumb />
            <div className="breadcrumb-wrapper d-flex flex-sm-row flex-column justify-content-sm-between justify-content-center align-items-stretch gap-3">
              <div className="page-details d-flex align-items-lg-center justify-content-start">
                <RiArticleFill size={40} className="object-fit-contain" />
                <div className="page-details-page-name">
                  <h3 className="mb-lg-2 mb-1 fw-bold">Document Settings</h3>
                  <p className="mb-0">Set up your document export settings</p>
                </div>
              </div>
              <Button
                type="button"
                variant=""
                className={`align-self-center bg-blue text-white rounded-3 w-auto fw-bold ${isMobileOnly ? "lh-1 p-4 d-flex justify-content-center align-items-center" : ""}`}
                disabled={isSubmitting}
                onClick={() =>
                  handleValidation(values, {
                    setErrors,
                    validateForm,
                    setSubmitting,
                  })
                }
                style={{
                  minWidth: "200px",
                  whiteSpace: "nowrap",
                  ...(isMobileOnly
                    ? {
                        width: "100%",
                        minWidth: "100%",
                      }
                    : {}),
                }}
              >
                {isSubmitting ? "Saving..." : "Save Settings"}
              </Button>
            </div>

            <hr className="m-0 border-0" />

            <Container fluid className="doc-settings">
              {isLoading ? (
                <p className="d-flex justify-content-center align-items-center w-100">
                  <Spinner />
                </p>
              ) : (
                <Row className="gx-5">
                  <Col lg={5} className="d-flex flex-column">
                    <FontSelection
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                    <LogoSelection
                      values={values}
                      setFieldValue={setFieldValue}
                      orgId={orgId}
                      organisation={organisation}
                    />
                  </Col>
                  <Col lg={7}>
                    <PreviewArea values={values} />
                  </Col>
                </Row>
              )}
            </Container>
          </main>
        </FormikForm>
      )}
    </Formik>
  );
}
