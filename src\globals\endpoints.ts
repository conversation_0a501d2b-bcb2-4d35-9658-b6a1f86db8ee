const createUrl = (...arg: string[]) => {
  return arg.map((a) => a.replace(/\/$/, "")).join("/");
};

export const API_ENDPOINTS = {
  // auth
  SIGNUP: "/users/signup",
  VERIFY_SIGNUP_OTP: "/users/signup/verify/otp",
  VERIFY_FORGOT_PASSWORD_OTP: "/users/forgetPassword/verify/otp",
  RESEND_OTP: "/users/resend/otp",
  RESEND_OTP_EMAIL: "/users/resend/email/verify/otp",
  FORGOT_PASSWORD: "/users/forgetPassword",
  RESET_PASSWORD: "/users/resetPassword",
  CHANGE_PASSWORD: "/users/changePassword",

  // cards
  GET_CARDS: "/cards",
  ADD_CARD: "/cards",
  DELETE_CARD: (card_id: any) => `cards/${card_id}`,
  SET_DEFAULT_CARD: (card_id: any) => `cards/${card_id}/set-default`,

  // chat
  CHAT_HISTORY: "/chat",
  SYNTHESIZE_TEXT: "/chat/synthesize",
  DELETE_HISTORY: (chat_id: any) =>
    `${createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai")}/${chat_id}`,
  MESSAGE_HISTORY: (chat_id: any) => `/chat/${chat_id}/messages`,
  EXPORT_CHAT: (chat_id: any) => `/chat/${chat_id}/export`,
  GET_PROMPTS: createUrl(import.meta.env.VITE_CHAT_API_URL, `chat_ai/prompt`),
  UPDATE_PROMPT: createUrl(import.meta.env.VITE_CHAT_API_URL, `chat_ai/prompt`),
  DELETE_PROMPT: (id: number) =>
    createUrl(import.meta.env.VITE_CHAT_API_URL, `chat_ai/prompt/${id}`),
  EXPORT_DOCUMENT: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/ai_export_setting`,
  ),

  // chat AI APIs
  GET_PRIVATE_AI_ENTITIES: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/private_ai_entities`,
  ),
  SEND_MESSAGE: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    `chat_ai/send_message`,
  ),
  UPLOAD_DOCUMENT: (chat_id: any) =>
    `${createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai")}/${chat_id}/upload_doc`,

  DELETE_DOCUMENT: ({ chat_id, msg_id, doc_id }: any) =>
    `${createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai")}/${chat_id}/${msg_id}/document/${doc_id}`,

  DELETE_SELECT_DOCUMENT: ({ chat_id, doc_id }: any) =>
    `${createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai")}/${chat_id}/document/${doc_id}`,

  UPDATE_BAD_RESPONSE: ({ chat_id, msg_id }: any) =>
    `${createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai")}/${chat_id}/${msg_id}/bad_response`,

  // notifications
  GET_NOTIFICATIONS: "/notifications",
  READ_NOTIFICATION: "/notifications/read",

  // organisation
  ORGANISATION: "/organization",
  INVITE_NEW: (id: any) => `/organization/${id}/invite-member`,
  ORGANISATION_MEMBERS: (id: any) => `/organization/${id}/members`,
  UPDATE_ORGANISATION: (id: any) => `/organization/${id}`,
  ORGANISATION_TEAM_PLAN: (id: any) => `/organization/${id}/team-plan`,
  UPDATE_ORG_PROMPT: (id: any) => `/organization/${id}/prompts`,
  DELETE_ORG_PROMPT: ({ org_id, prompt_id }: any) =>
    `/organization/${org_id}/prompts/${prompt_id}`,
  SAVE_ORG_DOCUMENT_SETTINGS: (id: any) =>
    `/organization/${id}/ai-export-settings`,
  DELETE_ORG_EXPORT_LOGO: (id: any) => `/organization/${id}/ai-export-settings`,
  UPGRADE_ORG_MEMBER_WORD_LIMIT: ({ org_id, member_id }: any) =>
    `/organization/${org_id}/members/${member_id}/word-limit`,

  // organisation members
  OFFLINE_ACCOUNT_TERMS: (token: any) =>
    `/organization-members/${token}/offline-account-terms`,
  ACCEPT_OFFLINE_ACCOUNT_TERMS:
    "/organization-members/accept-offline-account-terms",
  VERIFY_OFFLINE_ACCOUNT_TOKEN: (token: any) =>
    `/organization-members/${token}/verify-offline-account`,
  VERIFY_OFFLINE_ACCOUNT: "/organization-members/verify-offline-account",
  VERIFY_MEMBER_TOKEN: (token: any) =>
    `/organization-members/${token}/verify-account`,
  VERIFY_MEMBER_ACCOUNT: "/organization-members/verify-account",
  UPDATE_MEMBER_ROLE: (org_id: any, member_id: any) =>
    `/organization/${org_id}/members/${member_id}/role`,
  UPDATE_MEMBER_STATUS: (org_id: any, member_id: any) =>
    `/organization/${org_id}/members/${member_id}/status`,
  RESEND_INVITE: ({ member_id, org_id }: any) =>
    `/organization-members/${org_id}/resend-invitation/${member_id}`,

  // payment
  CREATE_PAYMENT_INTENT: "/payment/create/intent",
  GET_PAYMENT_CONFIG: "/payment/config",
  PAYMENT_STATUS: "/payment/status",
  PAYMENT_DETAILS: "/payment/details",
  UPDATE_UPCOMING_PLAN: "/payment/update-upcoming-plan",
  WORD_COUNT_PAYMENT_DETAILS: "/payment/issue-new-word-details",
  CREATE_WORD_COUNT_PAYMENT_INTENT: "/payment/create/intent/issue-new-words",
  WORD_COUNT_PAYMENT_STATUS: "/payment/issue-new-word-payment-status",

  // subscription
  GET_SUBSCRIPTIONS: "/subscription",
  MY_SUBSCRIPTION: "/subscription/mySubscription",
  CHECK_SUBSCRIPTION: "/users/check-subscription",
  GET_INVOICES: "/subscription/invoices",
  CURRENT_UPCOMING_SUBSCRIPTION: "/subscription/current-upcoming",
  GENERATE_INVOICE: ({ subscription_id, invoice_id }: any) =>
    `/subscription/${subscription_id}/invoice/${invoice_id}`,
  CANCEL_SUBSCRIPTION: (subscription_id: any) =>
    `/subscription/${subscription_id}/cancel`,
  APPLY_COUPON: "/subscription/apply-coupon",
  SUBSCRIPTION_TIERS: (id: any) => `/subscription/${id}/tiers`,

  // support
  SUGGEST_FEATURE: "/suggest-feature",
  REPORT_BUG: "/bug-report",

  // user
  MY_PROFILE: "/users/me",
  UPDATE_PROFILE: "/users/updateProfile",
  UPDATE_REDACT_SETTING: "/users/redact-settings",

  // utils
  CONTACT_US: "/contact-us",
  GET_STATIC_PAGE: "/pages",
  GET_CHAT_QUOTA: createUrl(import.meta.env.VITE_CHAT_API_URL, "chat_ai/quota"),
  GET_S3_PRE_SIGNED_URL: createUrl(
    import.meta.env.VITE_CHAT_API_URL,
    "chat_ai/upload_image",
  ),
  SAVE_DOCUMENT_SETTINGS: "/users/ai-export-settings",
  GET_DOCUMENT_SETTINGS: "/users/ai-export-settings",
  DELETE_EXPORT_LOGO: "/users/ai-export-settings",
  RESET_USER_PASSWORD: (user_id: any) => `/users/${user_id}/reset-password`,
};
