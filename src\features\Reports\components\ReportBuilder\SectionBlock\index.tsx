import {
  Ri<PERSON>dd<PERSON><PERSON>geLine,
  Ri<PERSON>rrowUpDownLine,
  RiCloseLargeLine,
  Ri<PERSON><PERSON>2<PERSON>ill,
  RiLockUnlockFill,
} from "@remixicon/react";
import Highlight from "@tiptap/extension-highlight";
import Image from "@tiptap/extension-image";
import Placeholder from "@tiptap/extension-placeholder";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import TextAlign from "@tiptap/extension-text-align";
import Underline from "@tiptap/extension-underline";
import { useEditor } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import CustomTiptapEditor from "components/Common/CustomTiptapEditor";
import EnhancedToolbar from "components/Common/CustomTiptapEditor/EnhancedToolbar";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";
import {
  removeReportBlockAtIndex,
  swapReportBlocks,
  updateReportBlockTitle,
  updateReportBlockContent,
} from "features/Reports/store";
import useReportStore from "features/Reports/store/report";
import { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import { useSectionLock } from "../hooks/useSectionLock";
import InsertSectionModal from "../InsertSectionModal";
import SelectionLockButton from "./SelectionLockButton";
import "./styles.scss";

const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  TableRow,
  TableHeader,
  TableCell,
  Image.configure({
    inline: true,
    allowBase64: true,
  }),
  GraphPlaceholder,
  ContentLock,
  Placeholder.configure({
    placeholder: "Start writing here...",
    emptyEditorClass: "is-editor-empty",
    showOnlyWhenEditable: true,
    showOnlyCurrent: true,
  }),
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
];

interface SectionBlockProps {
  title: string;
  content: string;
  index: number;
}

const SectionBlock = ({ title, content, index }: SectionBlockProps) => {
  const [showInsertModal, setShowInsertModal] = useState(false);
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [localTitle, setLocalTitle] = useState(title);
  const reportInfo = useReportStore((state) => state.reportInfo);
  const titleRef = useRef<HTMLInputElement>(null);
  const editor = useEditor({
    extensions,
    content,
  });

  const { lockState, toggleSectionLock } = useSectionLock({ editor });

  useEffect(() => {
    setLocalTitle(title);
  }, [title]);

  useEffect(() => {
    if (editor && content !== undefined) {
      try {
        // Content is now stored as HTML string
        if (typeof content === "string") {
          if (content !== editor.getHTML()) {
            editor.commands.setContent(content);
          }
        } else if (typeof content === "object" && content !== null) {
          // Handle legacy JSON content for backward compatibility
          const currentContent = editor.getJSON();
          if (JSON.stringify(content) !== JSON.stringify(currentContent)) {
            editor.commands.setContent(content);
          }
        }
      } catch (error) {
        console.error("Error updating editor content:", error);
      }
    }
  }, [editor, content]);

  // Auto-save content on editor changes
  useEffect(() => {
    if (!editor) return;

    const handleUpdate = () => {
      const currentContent = editor.getHTML();
      updateReportBlockContent(index, currentContent);
    };

    editor.on("update", handleUpdate);

    return () => {
      editor.off("update", handleUpdate);
    };
  }, [editor, index]);

  const handleTitleClick = () => {
    setIsEditingTitle(true);
    setTimeout(() => {
      if (titleRef.current) {
        titleRef.current.focus();
        titleRef.current.select();
      }
    }, 0);
  };

  const handleTitleSave = () => {
    if (titleRef.current) {
      const newTitle = titleRef.current.value.trim();
      if (newTitle && newTitle !== title) {
        updateReportBlockTitle(index, newTitle);
        setLocalTitle(newTitle);
      } else if (!newTitle) {
        setLocalTitle(title);
      }
      setIsEditingTitle(false);
    }
  };

  const handleTitleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleTitleSave();
    } else if (e.key === "Escape") {
      setLocalTitle(title);
      setIsEditingTitle(false);
    }
  };

  const handleTitleBlur = () => {
    handleTitleSave();
  };

  const handleSaveContent = () => {};

  const handleRemoveSection = () => {
    if (reportInfo.sections.length > 1) {
      removeReportBlockAtIndex(index);
    }
  };

  const handleReorderSection = () => {
    const nextIndex = index + 1;
    const prevIndex = index - 1;

    if (nextIndex < reportInfo.sections.length) {
      swapReportBlocks(index, nextIndex);
    } else if (prevIndex >= 0) {
      swapReportBlocks(index, prevIndex);
    }
  };

  return (
    <>
      <div className="section-block mb-4 p-3 rounded">
        <div className="section-header d-flex justify-content-between align-items-center mb-2">
          <div className="title d-flex align-items-center gap-1">
            <Button
              variant=""
              className={`section-lock-btn ${lockState.isFullyLocked ? "locked" : lockState.isPartiallyLocked ? "partial" : "unlocked"}`}
              onClick={toggleSectionLock}
              title={
                lockState.isFullyLocked
                  ? "Section fully locked - click to unlock all"
                  : lockState.isPartiallyLocked
                    ? "Section partially locked - click to lock all"
                    : "Section unlocked - click to lock all"
              }
            >
              {lockState.isFullyLocked ? (
                <RiLock2Fill color="#ad986f" />
              ) : (
                <RiLockUnlockFill color="#ad986f" />
              )}
            </Button>
            {isEditingTitle ? (
              <input
                ref={titleRef}
                type="text"
                className="section-title-input fw-bold"
                defaultValue={localTitle}
                onKeyDown={handleTitleKeyDown}
                onBlur={handleTitleBlur}
                autoFocus
              />
            ) : (
              <h5
                className="mb-0 fw-bold section-title-display"
                onClick={handleTitleClick}
                title="Click to edit title"
              >
                {localTitle}
              </h5>
            )}
          </div>
          <div className="controls">
            <Button
              variant=""
              onClick={() => setShowInsertModal(true)}
              title="Insert section"
            >
              <RiAddLargeLine color="#ad986f" />
            </Button>
            {reportInfo.sections.length > 1 && (
              <>
                <Button
                  variant=""
                  onClick={handleReorderSection}
                  title="Reorder section"
                >
                  <RiArrowUpDownLine color="#ad986f" />
                </Button>
                <Button
                  variant=""
                  onClick={handleRemoveSection}
                  title="Remove section"
                >
                  <RiCloseLargeLine color="#ad986f" />
                </Button>
              </>
            )}
          </div>
        </div>
        <div className="section-content">
          <EnhancedToolbar editor={editor} onSave={handleSaveContent} />
          <div className="editor-container position-relative">
            <CustomTiptapEditor editor={editor} />
            {!lockState.isFullyLocked && (
              <SelectionLockButton editor={editor} />
            )}
          </div>
        </div>
      </div>
      {showInsertModal && (
        <InsertSectionModal
          show={showInsertModal}
          onClose={() => setShowInsertModal(false)}
          index={index}
        />
      )}
    </>
  );
};

export default SectionBlock;
