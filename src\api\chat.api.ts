import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { ChatHistoryParamsInterface, SendMessagePayloadInterface } from "types";
import { getTimezone } from "utils";
import { validate as isValidUUID } from "uuid";
import { apiClient } from "./apiClient";

export interface ExportPayload {
  with_branding: boolean;
  content: any;
}

export const useSendMessageMutation = () =>
  useMutation({
    mutationFn: async (payload: SendMessagePayloadInterface) => {
      payload = { ...payload, timezone: getTimezone() };
      const response = await apiClient.post(
        API_ENDPOINTS.SEND_MESSAGE,
        payload,
      );
      return response;
    },
  });

export const usePrivateAIEntitiesQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.GET_PRIVATE_AI_ENTITIES,
      );
      return response?.data;
    },
    queryKey: ["private-ai-entities"],
    staleTime: Infinity,
  });

export const useUploadDocMutation = () =>
  useMutation({
    mutationFn: async ({
      chat_id,
      payload,
      setUploadProgress,
    }: Record<string, any>) => {
      const response = await apiClient.post(
        API_ENDPOINTS.UPLOAD_DOCUMENT(chat_id),
        payload,
        {
          onUploadProgress: (progressEvent: any) => {
            setUploadProgress((progressEvent.progress * 100).toFixed(1));
          },
        },
      );
      return response;
    },
  });

export const useMessageHistoryQuery = ({
  chat_id,
}: Record<string, string | undefined>) =>
  useQuery({
    queryFn: async () => {
      if (!isValidUUID(chat_id as string)) return;

      const response = await apiClient.get(
        API_ENDPOINTS.MESSAGE_HISTORY(chat_id),
      );
      return response?.data;
    },
    queryKey: ["message-history", chat_id],
    staleTime: Infinity,
    enabled: !!chat_id,
  });

export const useDeleteChatHistoryMutation = () =>
  useMutation({
    mutationFn: async (id: string) => {
      const response = await apiClient.delete(API_ENDPOINTS.DELETE_HISTORY(id));
      return response;
    },
  });

export const useChatHistoryQuery = (params: ChatHistoryParamsInterface) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.CHAT_HISTORY, {
        params,
      });
      return response?.data;
    },
    queryKey: ["chat-history"],
    staleTime: Infinity,
    refetchOnWindowFocus: false,
  });

export const useDeleteDocument = () =>
  useMutation({
    mutationFn: async ({ doc_id, chat_id, msg_id }: any) => {
      const response = await apiClient.delete(
        API_ENDPOINTS.DELETE_DOCUMENT({
          chat_id,
          msg_id,
          doc_id,
        }),
      );
      return response;
    },
  });

export const useDeleteSelectDocument = () =>
  useMutation({
    mutationFn: async ({ doc_id, chat_id }: any) => {
      const response = await apiClient.delete(
        API_ENDPOINTS.DELETE_SELECT_DOCUMENT({ chat_id, doc_id }),
      );
      return response;
    },
  });

export const useUpdateBadResponse = () =>
  useMutation({
    mutationFn: async ({ chat_id, msg_id }: any) => {
      const response = await apiClient.put(
        API_ENDPOINTS.UPDATE_BAD_RESPONSE({ chat_id, msg_id }),
      );
      return response;
    },
  });

export const useRemoveBadResponse = () =>
  useMutation({
    mutationFn: async ({ chat_id, msg_id }: any) => {
      const response = await apiClient.delete(
        API_ENDPOINTS.UPDATE_BAD_RESPONSE({ chat_id, msg_id }),
      );
      return response;
    },
  });

export const useSynthesizeText = () =>
  useMutation({
    mutationFn: async ({ message_id }: any) => {
      const response = await apiClient.get(API_ENDPOINTS.SYNTHESIZE_TEXT, {
        params: {
          message_id,
        },
      });
      return response;
    },
  });

export const useExportChatMutation = () =>
  useMutation({
    mutationFn: async ({ chat_id, ...payload }: any) => {
      const response = await apiClient.put(
        API_ENDPOINTS.EXPORT_CHAT(chat_id),
        payload,
      );
      return response;
    },
  });

export const useGetPromptsQuery = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_PROMPTS);
      return response?.data;
    },
    queryKey: ["custom-prompts"],
  });

export const useUpdatePromptMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.UPDATE_PROMPT,
        payload,
      );
      return response;
    },
  });

export const useDeletePromptMutation = () =>
  useMutation({
    mutationFn: async (id: number) => {
      const response = await apiClient.delete(API_ENDPOINTS.DELETE_PROMPT(id));
      return response;
    },
  });

export const useExportDocumentMutation = () =>
  useMutation({
    mutationFn: async ({ payload }: { payload: ExportPayload }) => {
      const response = await apiClient({
        url: API_ENDPOINTS.EXPORT_DOCUMENT,
        method: "POST",
        responseType: "blob",
        data: payload,
      });

      if (!(response instanceof Blob)) {
        console.error("Response is not a Blob:", response);
        throw new Error("Invalid file format received.");
      }

      return response;
    },
  });
