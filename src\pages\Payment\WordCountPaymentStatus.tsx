import { useWordCountPaymentStatusMutation } from "api";
import { HomeSuccess } from "pages/Home";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";

const WordCountPaymentStatus = () => {
  const [searchParams] = useSearchParams();
  const paymentIntentId = searchParams.get("payment_intent");
  const payment_status = searchParams.get("payment_status");
  const [loading, setLoading] = useState<boolean>(false);
  const { mutateAsync: paymentStatus } = useWordCountPaymentStatusMutation();
  const [status, setStatus] = useState<string | null>(null);

  useEffect(() => {
    if (paymentIntentId) {
      (async () => {
        setLoading(true);
        try {
          const result: any = await paymentStatus({
            payment_intent_id: paymentIntentId,
          });
          if (result?.success) {
            setStatus(result?.data?.status);
          }
        } catch (err: any) {
          console.log(err);
        } finally {
          setTimeout(() => {
            setLoading(false);
            window.history.replaceState(null, "", window.location.pathname);
          }, 2000);
        }
      })();
    }
  }, [paymentIntentId]);

  useEffect(() => {
    if (payment_status) {
      setStatus(payment_status);
    }
  }, [payment_status]);

  return (
    <HomeSuccess
      type={
        loading
          ? "LOADING"
          : status === "succeeded"
            ? "PAYMENT_SUCCESS"
            : "PAYMENT_FAILED"
      }
    />
  );
};

export default WordCountPaymentStatus;
