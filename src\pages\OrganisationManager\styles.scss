@use "/src/styles/mixins/mixins.scss";

.my-team-wrapper {
  padding: 30px;
  overflow: hidden;
  position: relative;
  height: calc(100vh - 195px);
  border-radius: 12px;
  box-shadow:
    0px 65px 47px 0px rgba(26, 26, 26, 0.04),
    0px 100px 80px 0px rgba(26, 26, 26, 0.05);

  @media only screen and (max-width: 991px) {
    height: auto;
  }

  @include mixins.slim-scrollbar;

  .table-responsive {
    max-height: calc(100vh - 570px) !important;
  }

  .custom-table {
    thead {
      th {
        min-width: 200px;

        @media only screen and (max-width: 991px) {
          min-width: 200px;

          &:nth-child(2) {
            min-width: 400px;
          }
        }

        &:last-child {
          text-align: center;
        }
      }
    }
    @include mixins.table-td-text-overflow;

    @media only screen and (min-width: 992px) {
      @include mixins.table-body-fix-height;
    }
  }

  .action-item-btn {
    width: 42px;
    height: 42px;
  }

  .status-pill {
    width: 100px;
    background-color: #60a799;

    &.active {
      background-color: #60a799;
    }

    &.pending {
      background-color: #b04912;
      border-color: #b04912;
      color: #f9f9f9;
    }

    &.cancel,
    &.blocked {
      background-color: #b01212;
      border-color: #b01212;
      color: #f9f9f9;
    }
  }

  .loader-icon {
    width: 25px;
    height: 25px;
  }

  hr {
    height: 10px;
    background-image: url("/src/assets/images/tableDivider.webp");
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
  }

  .status-dropdown {
    .btn {
      border-radius: 50px;
      width: 140px;
      height: auto;
      padding: 6px 30px;
      font-weight: bold;
      letter-spacing: 0.6px;
      text-transform: capitalize;
    }

    &.active {
      .btn {
        background-color: #60a799 !important;
        border-color: #60a799 !important;
        color: #f9f9f9;
      }
    }

    &.pending {
      .btn {
        background-color: #b03c12 !important;
        border-color: #b03c12 !important;
        color: #f9f9f9;
      }
    }

    &.deactivate {
      .btn {
        display: flex !important;
        justify-content: space-between;
        align-items: center;
        background-color: #e6e6e6 !important;
        border-color: #e6e6e6 !important;
        color: #70828d !important;
      }
    }

    .dropdown-item {
      &:active {
        background-color: #e6e6e6 !important;
        color: inherit !important;
      }

      svg {
        fill: #70828d;
        width: 20px;
        vertical-align: top;
        margin-right: 10px;
      }
    }
  }

  .action-btns {
    .submit-btn {
      @include mixins.submit-btn;

      &.bg-transparent {
        @include mixins.submit-btn-transparent;
        @include mixins.button-layer-hover;
      }
    }
  }
}
