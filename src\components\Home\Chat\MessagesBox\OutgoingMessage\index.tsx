import ReactMarkdown from "react-markdown";
import remarkBreaks from "remark-breaks";
import remarkGfm from "remark-gfm";
import DocumentCard from "./DocumentCard";
import ImageCard from "./ImageCard";
import "./styles.scss";

const LinkRenderer = (props: any) => {
  return (
    <a href={props.href} target="_blank" rel="noreferrer">
      {props.children}
    </a>
  );
};

const OutgoingMessage = ({ messageItem }: any) => {
  const { text, metadata, message_type, id, is_doc_deleted } =
    messageItem ?? {};

  return (
    <div className="outgoing">
      <div className="outgoing-data">
        <div className="outgoing-data-details text-end d-flex flex-column">
          <p className="user-name m-0 fw-bold">You</p>

          {!is_doc_deleted && (
            <>
              {message_type === "media" && (
                <ImageCard metadata={metadata} msgId={id} />
              )}
              {message_type === "document" && (
                <DocumentCard metadata={metadata} msgId={id} />
              )}
            </>
          )}

          <div className="user-message-text text-start bg-blue font-light">
            <ReactMarkdown
              remarkPlugins={[remarkGfm, remarkBreaks]}
              components={{ a: LinkRenderer }}
            >
              {text}
            </ReactMarkdown>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OutgoingMessage;
