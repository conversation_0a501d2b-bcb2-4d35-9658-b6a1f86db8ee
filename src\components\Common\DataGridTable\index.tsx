import React from "react";
import { Table } from "react-bootstrap"; // Add Spinner for loading indication
import { LoaderIcon } from "react-hot-toast";
import CustomPagination from "../DataTable/CustomPagination";
import SortArrow from "../DataTable/SortArrow";
import "./styles.scss";

const DataGridTable = ({
  paginationProps,
  columns,
  rows,
  sortDirection,
  onSort,
  addBlankRowAfterEach = false,
  customClassName = {
    body: "simple-table",
  },
  showFooter = true,
  loading = false,
  filters,
  onRowClick = () => {},
}: any) => {
  return (
    <>
      <Table
        borderless
        hover
        responsive
        className="custom-table mb-0 align-middle position-relative"
      >
        <thead className="position-sticky">
          <tr>
            <th colSpan={columns.length}>
              <hr className="mt-0 mb-2 p-0 border-0 w-100 d-block" />
            </th>
          </tr>

          <tr>
            {columns.map((col: any, index: number) => (
              <th key={index} className="text-center align-middle">
                <div
                  className="d-flex align-items-center"
                  style={{ gap: "10px" }}
                >
                  <p className="mb-0 text-center">
                    {col?.headerName}
                    {col?.subHeader && (
                      <>
                        <br />
                        <span className="mb-0 fw-normal">{col?.subHeader}</span>
                      </>
                    )}
                  </p>
                  {col?.isSortable && (
                    <SortArrow
                      direction={
                        sortDirection &&
                        (col?.sortableColumn === filters?.sort_column
                          ? sortDirection
                          : "desc")
                      }
                      onClick={() => onSort(col?.sortableColumn)}
                    />
                  )}
                </div>
              </th>
            ))}
          </tr>

          <tr>
            <th colSpan={columns.length}>
              <hr className="mb-0 mt-2 p-0 border-0 w-100 d-block" />
            </th>
          </tr>
        </thead>

        <tbody className={customClassName?.body}>
          {loading ? (
            <tr>
              <td colSpan={columns.length} className="text-center">
                <LoaderIcon className="mx-auto table-loader" />
              </td>
            </tr>
          ) : rows?.length > 0 ? (
            rows.map((row: any, rowIndex: number) => (
              <React.Fragment key={rowIndex}>
                <tr onClick={() => onRowClick(row)} className="cursor-pointer">
                  {columns.map((col: any, colIndex: number) => (
                    <td key={colIndex}>
                      {col.renderCell ? col.renderCell(row) : row[col.field]}
                    </td>
                  ))}
                </tr>

                {addBlankRowAfterEach && rowIndex !== rows.length - 1 && (
                  <tr>
                    <td colSpan={columns.length} className="blank-row"></td>
                  </tr>
                )}
              </React.Fragment>
            ))
          ) : (
            <tr>
              <td colSpan={columns.length}>
                <p className="mb-0 text-danger fw-bold text-center">
                  No data available
                </p>
              </td>
            </tr>
          )}
        </tbody>
      </Table>

      {showFooter && !loading && <CustomPagination {...paginationProps} />}
    </>
  );
};

export default DataGridTable;
