import { OrgUserRole } from "globals";
import useUserStore from "stores/user";

export default function useUserStatus() {
  const user = useUserStore((state) => state.userInfo.user);

  const isOrgAdmin = user?.organizationMember?.role === OrgUserRole.ADMIN;
  const isOfflineAccount = !!user?.is_offline_account;
  const isIndividualUser = !!(
    user &&
    !user.ownedOrganization &&
    !user.organizationMember
  );

  return {
    user,
    isOrgAdmin,
    isOfflineAccount,
    isIndividualUser,
  };
}
