import { RiSparkling2Line } from "@remixicon/react";
import { DefaultProfile } from "components/Common";
import { Image, Offcanvas } from "react-bootstrap";
import { Link } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import { generateNickName } from "utils";
import HistoryBox from "../HistoryBox";
import "./styles.scss";
import { SETTINGS } from "globals";

interface HistoryOffcanvasProps {
  show: boolean;
  handleClose: () => void;
}

const HistoryOffcanvas: React.FC<HistoryOffcanvasProps> = ({
  show,
  handleClose,
}) => {
  const user = useUserStore((state) => state.userInfo.user);

  const handleCloseOnClick = () => {
    handleClose();
  };

  return (
    <Offcanvas
      show={show}
      onHide={handleClose}
      className="chat-history-offcanvas"
    >
      <Offcanvas.Header
        closeButton
        className="chat-history-offcanvas-header flex-wrap pb-0"
      >
        <Offcanvas.Title>
          <p className="mb-0 page-title fw-bold">{SETTINGS.APP_NAME}</p>
        </Offcanvas.Title>
        <hr className="mt-0 d-block w-100 order-3" />
      </Offcanvas.Header>

      <Offcanvas.Body className="chat-history-offcanvas-body p-0">
        <HistoryBox onCloseCanvas={handleCloseOnClick} />

        <div className="history-footer position-sticky bottom-0 d-flex flex-column bg-light">
          <Link
            to={ROUTE_PATH.SUBSCRIPTIONS}
            onClick={handleCloseOnClick}
            className="history-footer-profile d-flex font-primary text-decoration-none"
            style={{ gap: "10px" }}
          >
            <RiSparkling2Line
              color="#0d3149"
              size={"35px"}
              className="rounded-circle"
            />
            <div>
              <h6 className="fw-bold mb-1">Upgrade plan</h6>
              <small className="mb-0">upgrade your plan today</small>
            </div>
          </Link>

          <Link
            to={ROUTE_PATH.SETTINGS}
            onClick={handleCloseOnClick}
            className="history-footer-plans d-flex font-primary text-decoration-none fw-bold align-items-center text-truncate"
          >
            {user?.profile_photo ? (
              <Image
                src={user?.profile_photo}
                className="object-fit-cover profile-data-img rounded-circle bg-brown"
                alt="user"
              />
            ) : (
              <DefaultProfile
                text={generateNickName(user?.full_name)}
                className="small"
              />
            )}
            <span className="text-truncate">{user?.full_name}</span>
          </Link>
        </div>
      </Offcanvas.Body>
    </Offcanvas>
  );
};

export default HistoryOffcanvas;
