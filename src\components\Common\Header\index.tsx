import { useAuth0 } from "@auth0/auth0-react";
import {
  RiLogoutBoxRLine,
  RiMenu2Line,
  RiNotification2Fill,
  RiSettings4Fill,
} from "@remixicon/react";
import { HistoryOffcanvas } from "components/Home/Chat";
import { LOGIN_TYPE } from "globals";
import { useEffect, useState } from "react";
import { Button, Image, Nav, Navbar } from "react-bootstrap";
import { isDesktop, isMobile } from "react-device-detect";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import {
  resetEntityConfiguration,
  resetPlanState,
  setIsLoggingOut,
} from "stores";
import useUserStore, { resetUserState } from "stores/user";
import useUtilStore, { setTempOrgProfile } from "stores/util";
import { generateNickName } from "utils";
import DefaultProfile from "../DefaultProfile";
import HoverTooltip from "../HoverTooltip";
import "./styles.scss";
import { useHeaderConfig } from "hooks";

const Header = () => {
  const [show, setShow] = useState(false);
  const { logout, isAuthenticated } = useAuth0();
  const { user, loginType } = useUserStore((state) => state?.userInfo);
  const tempOrgProfile: any = useUtilStore((state) => state.tempOrgProfile);

  const handleClose = () => setShow(false);
  const handleShow = () => setShow(true);
  const navigate = useNavigate();

  const location = useLocation();
  const headerConfig = useHeaderConfig({ user });

  const handleLogout = () => {
    setIsLoggingOut(true);
    resetEntityConfiguration();
    if (isAuthenticated && loginType === LOGIN_TYPE.SSO) {
      logout();
    }
    resetUserState();
    resetPlanState();
    navigate(ROUTE_PATH.LOGIN);
    setTimeout(() => {
      setIsLoggingOut(false);
    }, 100);
  };

  useEffect(() => {
    if (tempOrgProfile && tempOrgProfile?.formData) {
      const allowedRoutes = [
        ROUTE_PATH.DOC_SETTINGS,
        ROUTE_PATH.ORGANISATIONAL_PROFILE,
      ];
      if (!allowedRoutes.includes(location.pathname)) {
        setTempOrgProfile(null);
      }
    }
  }, [location.pathname]);

  const renderSuperuserWarning = () => {
    return null;
    // if (!user?.superadmin_offline_access) return null;

    // if (isMobile) {
    //   return (
    //     <sub
    //       style={{
    //         backgroundColor: "#fff3cd",
    //         color: "#856404",
    //         borderRadius: "5px",
    //       }}
    //       className="d-block"
    //     >
    //       Warning: Logged in as Super User
    //     </sub>
    //   );
    // }

    // if (isDesktop) {
    //   return (
    //     <div className="info-bar">
    //       <strong>Warning:</strong> You are currently logged in as a Super User.
    //     </div>
    //   );
    // }

    // return null;
  };

  return (
    <>
      <header className="header">
        <Navbar className="header-nav align-items-stretch py-0">
          <Nav className="header-nav-left d-flex align-items-center justify-content-lg-start justify-content-between">
            <div
              className={`d-flex justify-content-center align-items-center ${user?.superadmin_offline_access && isDesktop ? "justify-content-between w-100 gap-3" : ""}`}
            >
              <Button
                variant="Link"
                onClick={handleShow}
                className="d-lg-none d-block border-0"
              >
                <RiMenu2Line size={"20px"} color="black" />
              </Button>

              <div className="d-flex align-items-center gap-5">
                {headerConfig.map((item, idx) => (
                  <Link
                    key={idx}
                    to={item.path}
                    className={`mb-0 text-decoration-none font-primary ${location.pathname === item.path || item?.isDefault ? "page-title" : ""}`}
                  >
                    {item.label}
                    {idx === 0 && isMobile && renderSuperuserWarning()}
                  </Link>
                ))}
              </div>
              {isDesktop && renderSuperuserWarning()}
            </div>

            <div className="d-flex align-items-center gap-3">
              {user?.is_subscription && (
                <Nav.Link
                  as={Link}
                  to={ROUTE_PATH.NOTIFICATIONS}
                  className="bg-brown nav-link text-center d-lg-none d-flex align-items-center justify-content-center rounded-2 position-relative"
                >
                  <RiNotification2Fill size={"18px"} color="#f9f9f9" />

                  {user?.has_unread_notification && (
                    <span className="notification-dot position-absolute bg-danger rounded-circle"></span>
                  )}
                </Nav.Link>
              )}

              <Button
                variant="link"
                className="bg-brown nav-link text-center d-lg-none d-flex align-items-center justify-content-center rounded-2"
                onClick={handleLogout}
              >
                <RiLogoutBoxRLine size={"18px"} color="#f9f9f9" />
              </Button>
            </div>
          </Nav>

          <Nav className="header-nav-right bg-blue d-lg-flex align-items-center d-none">
            <div className="profile-data d-flex">
              {user?.profile_photo ? (
                <Image
                  src={user?.profile_photo}
                  className="object-fit-cover profile-data-img rounded-circle bg-brown"
                  alt="user"
                />
              ) : (
                <DefaultProfile
                  text={generateNickName(user?.full_name)}
                  className="small text-uppercase"
                />
              )}

              <div className="profile-data-name font-light d-flex align-items-center">
                <p
                  className="mb-0 text-truncate text-capitalize"
                  style={{ maxWidth: "150px" }}
                >
                  {user?.first_name}
                </p>

                {/* <p className="mb-0">Adviser</p> */}
              </div>
            </div>

            <div className="d-flex" style={{ gap: "6px" }}>
              {/* <Nav.Link className="bg-brown text-center d-flex align-items-center justify-content-center">
                <Form>
                  <Form.Check
                    type="switch"
                    id="custom-switch"
                    className="p-0"
                  />
                </Form>
              </Nav.Link> */}

              {user?.is_subscription && (
                <>
                  <HoverTooltip title="Notifications" customClass="fw-bold">
                    <Nav.Link
                      as={Link}
                      to={ROUTE_PATH.NOTIFICATIONS}
                      className="bg-brown text-center d-flex align-items-center justify-content-center position-relative"
                    >
                      <RiNotification2Fill size={"24px"} color="#f9f9f9" />

                      {user?.has_unread_notification && (
                        <span className="notification-dot position-absolute bg-danger rounded-circle"></span>
                      )}
                    </Nav.Link>
                  </HoverTooltip>

                  <HoverTooltip title="Settings" customClass="fw-bold">
                    <Link
                      to={ROUTE_PATH.SETTINGS}
                      className="bg-brown nav-link text-center d-flex align-items-center justify-content-center"
                    >
                      <RiSettings4Fill size={"24px"} color="#f9f9f9" />
                    </Link>
                  </HoverTooltip>
                </>
              )}

              <HoverTooltip title="Logout" customClass="fw-bold">
                <Button
                  variant="link"
                  className="bg-brown nav-link text-center d-flex align-items-center justify-content-center"
                  onClick={handleLogout}
                >
                  <RiLogoutBoxRLine size={"24px"} color="#f9f9f9" />
                </Button>
              </HoverTooltip>
            </div>
          </Nav>
        </Navbar>
      </header>

      <HistoryOffcanvas show={show} handleClose={handleClose} />
    </>
  );
};

export default Header;
