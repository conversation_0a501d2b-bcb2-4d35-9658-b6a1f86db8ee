import { BackBreadcrumb, DataGridTable, OrganisationToolbar } from "components";
import CreateOrganisation from "./CreateOrganisation";
import "./styles.scss";
import { useOrgManagerConfig } from "./useOrgManagerConfig";

const OrganisationManager = () => {
  const { organization, toolbarProps, dataGridProps } = useOrgManagerConfig();

  return (
    <main className="my-team-wrapper bg-white">
      <BackBreadcrumb />
      {organization?.id ? (
        <>
          <OrganisationToolbar {...toolbarProps} />
          <DataGridTable {...dataGridProps} />
        </>
      ) : (
        <CreateOrganisation />
      )}
    </main>
  );
};

export default OrganisationManager;
