import { useVerifyOTPMutation } from "api";
import { API_ENDPOINTS } from "globals";
import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Spin<PERSON> } from "react-bootstrap";
import { useNavigate, useSearchParams } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import AuthHeaderData from "../AuthHeaderData";
import OTPInputComponent from "./OTPInputComponent";
import ResendOTP from "./ResendOTP";
import "./styles.scss";

const OTPVerify = () => {
  const [searchParams] = useSearchParams();
  const otpId = searchParams.get("id");
  const email = searchParams.get("email");
  const type = searchParams.get("type") ?? "";
  const navigate = useNavigate();

  const [otp, setOTP] = useState<string>("");
  const [error, setError] = useState<string | null>(null);
  const [message, setMessage] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const { mutateAsync: verifyOTPMutation } = useVerifyOTPMutation();

  const handleVerify = async () => {
    if (otp?.length !== 4) {
      setError("Please enter OTP");
      return;
    }
    setIsLoading(true);
    setError(null);
    const payload = {
      id: otpId,
      code: otp,
    };
    const { API_URL, REDIRECT_URL } = VERIFY_REDIRECT_URL[type];
    try {
      const result: any = await verifyOTPMutation({
        url: API_URL,
        payload,
      });
      if (result?.success) {
        const { reset_password_token } = result?.data ?? {};
        navigate(
          reset_password_token
            ? `${REDIRECT_URL}?resetPasswordToken=${reset_password_token}`
            : REDIRECT_URL,
        );
      } else {
        console.error(result?.message);
      }
    } catch (err) {
      console.log(err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (otp?.length === 4) {
      handleVerify();
    }

    const keyDownHandler = (event: any) => {
      if (event?.key === "Enter") {
        handleVerify();
      }
    };

    document.addEventListener("keydown", keyDownHandler);

    return () => {
      document.removeEventListener("keydown", keyDownHandler);
    };
  }, [otp]);

  const headerData = {
    heading: "Verify account",
    description: `A 4 digit code has been sent to your registered email <br class="d-lg-block d-none" /> ID ${email}`,
  };

  const VERIFY_REDIRECT_URL: any = {
    forgotPassword: {
      API_URL: API_ENDPOINTS?.VERIFY_FORGOT_PASSWORD_OTP,
      REDIRECT_URL: ROUTE_PATH.RESET,
    },
    signUp: {
      API_URL: API_ENDPOINTS?.VERIFY_SIGNUP_OTP,
      REDIRECT_URL: ROUTE_PATH.OTP_SUCCESS,
    },
    login: {
      API_URL: API_ENDPOINTS?.VERIFY_SIGNUP_OTP,
      REDIRECT_URL: ROUTE_PATH.OTP_SUCCESS,
    },
  };

  return (
    <div
      className="auth-form d-flex justify-content-center align-items-center flex-column otp-form"
      style={{ gap: "30px" }}
    >
      <AuthHeaderData
        heading={headerData.heading}
        description={headerData.description}
      />

      <div className="website-form">
        <form className="d-flex flex-column" style={{ gap: "30px" }}>
          <OTPInputComponent
            otp={otp}
            setOTP={setOTP}
            message={message}
            error={error}
            setError={setError}
          />

          <div
            className="action-btns d-flex flex-column"
            style={{ gap: "30px" }}
          >
            <Button
              type="button"
              className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
              onClick={handleVerify}
              disabled={isLoading}
            >
              {isLoading ? <Spinner /> : "Submit"}
            </Button>
          </div>

          <div className="interaction-btns d-flex align-items-center font-gray">
            <ResendOTP
              otpId={otpId}
              setMessage={setMessage}
              setOTP={setOTP}
              type={type}
              email={email}
            />
          </div>
        </form>
      </div>
    </div>
  );
};

export default OTPVerify;
