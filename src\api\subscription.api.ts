import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";

export const useGetSubscriptionPlans = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_SUBSCRIPTIONS);
      return response?.data;
    },
    queryKey: ["subscription-plans"],
  });

export const useGetMySubscription = ({ params }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.MY_SUBSCRIPTION, {
        params,
      });
      return response?.data;
    },
    queryKey: ["my-subscription"],
  });

export const useCheckSubscription = () =>
  useMutation({
    mutationFn: (token: string | null) => {
      return apiClient.get(API_ENDPOINTS.CHECK_SUBSCRIPTION, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
    },
  });

export const useCheckUserSubscription = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.CHECK_SUBSCRIPTION);
      return response?.data;
    },
    queryKey: ["check-user-subscription"],
  });

export const useGenerateInvoice = () =>
  useMutation({
    mutationFn: ({ subscription_id, invoice_id }: any) => {
      return apiClient.get(
        API_ENDPOINTS.GENERATE_INVOICE({
          subscription_id,
          invoice_id,
        }),
      );
    },
  });

export const useCancelSubscriptionMutation = () =>
  useMutation({
    mutationFn: ({ subscription_id }: any) => {
      return apiClient.post(API_ENDPOINTS.CANCEL_SUBSCRIPTION(subscription_id));
    },
  });

export const useGetInvoices = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_INVOICES);
      return response?.data;
    },
    queryKey: ["invoices"],
  });

export const useCurrentUpcomingSubscription = ({ options }: any = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.CURRENT_UPCOMING_SUBSCRIPTION,
      );
      return response?.data;
    },
    queryKey: ["current-upcoming-subscription"],
    ...options,
  });

export const useApplyCoupon = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.APPLY_COUPON,
        payload,
      );
      return response;
    },
  });

export const useSubscriptionTiers = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.SUBSCRIPTION_TIERS(id),
      );
      return response?.data;
    },
    queryKey: ["subscription-tiers", id],
    enabled: !!id,
  });
