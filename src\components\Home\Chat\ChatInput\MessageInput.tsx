import { SETTINGS } from "globals";
import { useEffect } from "react";
import { Form } from "react-bootstrap";
import { isMobileOnly } from "react-device-detect";

interface MessageInputProps {
  onSendMessage: () => void;
  textareaRef: any;
  isDragging: boolean;
  isResponseProcessing: boolean;
  onPaste: (e: any) => void;
}

const MessageInput = ({
  onSendMessage,
  textareaRef,
  isDragging,
  isResponseProcessing,
  onPaste,
}: MessageInputProps) => {
  const handleInput = () => {
    if (textareaRef.current) {
      const minHeight = 54;
      const maxHeight = 150;
      textareaRef.current.style.height = `${minHeight}px`;
      const { scrollHeight } = textareaRef.current;
      let newHeight = scrollHeight > minHeight ? scrollHeight : minHeight;
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
      }
      textareaRef.current.style.height = `${newHeight}px`;
    }
  };

  useEffect(() => {
    handleInput();
  }, [textareaRef?.current?.value]);

  return (
    <Form.Group className="m-0 p-0">
      <Form.Control
        style={{
          height: "54px",
          transition: "all -.4s ease",
          fontSize: isMobileOnly ? "0.9rem" : "",
        }}
        as="textarea"
        className="border-0 fw-medium"
        placeholder={
          !isDragging
            ? `Ask ${isMobileOnly ? "" : SETTINGS.APP_NAME} a question`
            : ""
        }
        onInput={handleInput}
        ref={textareaRef}
        onKeyDown={(evt: any) => {
          if (evt.key === "Enter" && !evt.shiftKey) {
            evt.preventDefault();
            if (!isResponseProcessing) {
              onSendMessage();
            }
          }
        }}
        disabled={isDragging}
        onPaste={onPaste}
      />
    </Form.Group>
  );
};

export default MessageInput;
