import { ErrorMessage } from "formik";
import { useEffect, useState } from "react";
import { Dropdown } from "react-bootstrap";

const CustomSelectDropdown = ({
  label,
  options,
  customPlaceholder,
  note,
  additionalClass = "",
  alwaysShowNote = false,
  setFieldValue,
  name,
  value,
  renderError,
}: any) => {
  const [selectedItem, setSelectedItem] = useState(customPlaceholder);

  const handleSelect = (eventKey: any) => {
    const keyData = JSON.parse(eventKey) || {};
    setSelectedItem(keyData);

    if (setFieldValue) {
      setFieldValue(name, keyData?.value);
    }
  };

  useEffect(() => {
    if (value) {
      setSelectedItem(
        options.find(
          (option: any) =>
            option.value === (isNaN(Number(value)) ? value : Number(value)),
        ),
      );
    }
  }, [value, options]);

  return (
    <div className={`custom-dropdown-select ${additionalClass}`}>
      <label className="form-label">{label}</label>

      <Dropdown onSelect={handleSelect}>
        <Dropdown.Toggle className="w-100 form-control text-start d-flex align-items-center justify-content-start">
          <p
            className={`mb-0 select-value ${selectedItem?.label ? "font-primary opacity-100" : ""}`}
          >
            {selectedItem?.label ?? selectedItem}
          </p>
        </Dropdown.Toggle>

        <Dropdown.Menu>
          {options.map((option: any, index: number) => (
            <Dropdown.Item
              key={index}
              eventKey={JSON.stringify(option)}
              as="p"
              className="fw-500 cursor-pointer mb-0"
            >
              {option?.label}
            </Dropdown.Item>
          ))}
        </Dropdown.Menu>
        {name &&
          (renderError ? (
            renderError(name)
          ) : (
            <ErrorMessage component={"span"} name={name} />
          ))}
      </Dropdown>

      {(alwaysShowNote || selectedItem !== customPlaceholder) && note && (
        <p className="mb-0 mt-lg-4 mt-2 input-field-notes">
          <small className="fw-bold">Note:</small> {note}
        </p>
      )}
    </div>
  );
};

export default CustomSelectDropdown;
