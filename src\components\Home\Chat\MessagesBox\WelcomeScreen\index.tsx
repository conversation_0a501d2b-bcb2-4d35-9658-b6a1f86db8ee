import { useGetPromptsQuery } from "api";
import { Prompts } from "components/Common";
import { DEFAULT_PROMPTS, OrgUserRole } from "globals";
import { useMemo } from "react";
import { Col, Container, Row } from "react-bootstrap";
import "./styles.scss";
import { getOrganisationInfo } from "stores";
import { useUserRoles } from "hooks";

const WelcomeScreen = ({ containerStyle }: any) => {
  const { data = [], isLoading, isFetching } = useGetPromptsQuery();
  const organisation = getOrganisationInfo();
  const userRole = useUserRoles();
  const isDisableDefaultPrompts =
    organisation?.settings?.disable_default_prompts &&
    userRole === OrgUserRole.USER;

  const prompts = useMemo(() => {
    const savedPromptsMap = new Map(
      data.map((item: any) => [item.prompt_id, item]),
    );

    const mergedPrompts = DEFAULT_PROMPTS.map((defaultPrompt) => {
      return savedPromptsMap.get(defaultPrompt.prompt_id) || defaultPrompt;
    });

    data.forEach((item: any) => {
      if (!DEFAULT_PROMPTS.some((p) => p.prompt_id === item.prompt_id)) {
        mergedPrompts.push(item);
      }
    });

    return isDisableDefaultPrompts
      ? []
      : mergedPrompts.sort((a: any, b: any) => a.prompt_id - b.prompt_id);
  }, [data, isDisableDefaultPrompts]);

  return (
    <>
      <div className="welcome-screen" style={containerStyle}>
        <Container fluid className="h-100">
          <Row
            className="flex-xl-column align-items-center justify-content-center h-100"
            style={{ gap: "10px" }}
          >
            <Col sm="12" md="11" xl="11" xxl="8">
              <h1 className="mb-0 heading font-primary text-capitalize text-center">
                Powerful AI. Secure data
              </h1>

              <hr className="p-0 opacity-100 m-0 border-0 w-25 mx-auto mt-3" />
            </Col>

            <Col
              sm="12"
              md="11"
              xl="11"
              xxl="8"
              className="mt-xl-5 mt-3 position-relative"
            >
              <Prompts
                isLoading={isLoading || isFetching}
                prompts={prompts.filter((item: any) => !item?.organization_id)}
                allStoredPrompts={data}
                storedOrgPrompts={data?.filter(
                  (item: any) => item?.organization_id,
                )}
              />
            </Col>
          </Row>
        </Container>
      </div>
    </>
  );
};

export default WelcomeScreen;
