import { useAuth0 } from "@auth0/auth0-react";
import { useCheckSubscription, useMyProfileMutation } from "api";
import { LoadingOverlay } from "components";
import { AUTH0_INFO, LOGIN_TYPE } from "globals";
import { useEffect, useState } from "react";
import { Outlet, useLocation } from "react-router-dom";
import { setEntityConfiguration } from "stores";
import useUserStore, {
  resetUserState,
  setSubscriptionInfo,
  setUserInfo,
} from "stores/user";
import { processHash } from "utils";

const PublicLayout = () => {
  const { isAuthenticated, isLoading, getAccessTokenSilently } = useAuth0();
  const { token, loginType } = useUserStore((state) => state.userInfo);
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const { mutateAsync: myProfile } = useMyProfileMutation();
  const { mutateAsync: checkSubscription } = useCheckSubscription();

  const fetchUserProfile = async (token: string, loginType: string) => {
    try {
      const [userResult, subscriptionResult]: any = await Promise.all([
        myProfile(token),
        checkSubscription(token),
      ]);
      if (userResult?.success) {
        setUserInfo({
          token,
          loginType,
          user: {
            ...userResult?.data,
            is_subscription: subscriptionResult?.data?.is_subscription,
          },
        });
        setSubscriptionInfo(subscriptionResult?.data?.subscription ?? {});
        if (userResult?.data?.redact_setting) {
          setEntityConfiguration({
            ...userResult?.data?.redact_setting,
            enable_privacy:
              userResult?.data?.redact_setting?.enable_privacy ?? true,
          });
        }
      }
    } catch (err: any) {
      resetUserState();
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    let auth0Token = null;
    (async () => {
      if (loginType === LOGIN_TYPE.SSO && !token) {
        try {
          setLoading(true);
          auth0Token = await getAccessTokenSilently({
            authorizationParams: {
              audience: AUTH0_INFO.AUTH0_AUDIENCE,
            },
          });
          if (isAuthenticated && !isLoading && auth0Token) {
            fetchUserProfile(auth0Token, LOGIN_TYPE.SSO);
          }
        } catch (err: any) {
          resetUserState();
        } finally {
          setLoading(false);
        }
      }
    })();
  }, [isAuthenticated, isLoading, loading, setLoading]);

  useEffect(() => {
    if (location.hash) {
      processHash(location.hash, setLoading, fetchUserProfile);
    }
  }, [location.hash, token, loginType]);

  if (loading) {
    return <LoadingOverlay show={true} />;
  }

  return <Outlet />;
};

export default PublicLayout;
