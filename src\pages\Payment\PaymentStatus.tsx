import { usePaymentStatusMutation } from "api";
import { HomeSuccess } from "pages/Home";
import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { setUserInfo } from "stores";
import useUserStore, { setSubscriptionInfo } from "stores/user";

const PaymentStatus = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const paymentIntentId = searchParams.get("payment_intent");
  const isSubscription = searchParams.get("is_subscription");
  const upcomingPlanStatus = searchParams.get("upcoming_plan_status");
  const is_fully_paid = searchParams.get("is_fully_paid");
  const [loading, setLoading] = useState<boolean>(false);
  const { mutateAsync: paymentStatus } = usePaymentStatusMutation();
  const [status, setStatus] = useState<string | null>(null);

  const userInfo = useUserStore((state) => state.userInfo);

  useEffect(() => {
    if (paymentIntentId) {
      (async () => {
        setLoading(true);
        try {
          const result: any = await paymentStatus({
            payment_intent_id: paymentIntentId,
          });
          if (result?.success) {
            setStatus(result?.data?.status);
            if (result?.data?.subscription) {
              setSearchParams({
                is_subscription: "true",
              });
              setSubscriptionInfo(result?.data?.subscription ?? {});
            }
          }
        } catch (err: any) {
          console.log(err);
        } finally {
          setTimeout(() => {
            setLoading(false);
            window.history.replaceState(null, "", window.location.pathname);
          }, 2000);
        }
      })();
    }
  }, [paymentIntentId]);

  useEffect(() => {
    if (is_fully_paid) {
      setStatus("succeeded");
      setSearchParams({
        is_subscription: "true",
      });
    }
  }, [is_fully_paid]);

  useEffect(() => {
    if (isSubscription) {
      setUserInfo({
        ...userInfo,
        user: {
          ...userInfo.user,
          is_subscription: true,
        },
      });
    }
  }, [isSubscription]);

  useEffect(() => {
    if (upcomingPlanStatus) {
      setStatus(upcomingPlanStatus);
    }
  }, [upcomingPlanStatus]);

  return (
    <HomeSuccess
      type={
        loading
          ? "LOADING"
          : status === "succeeded" || isSubscription
            ? "PAYMENT_SUCCESS"
            : "PAYMENT_FAILED"
      }
    />
  );
};

export default PaymentStatus;
