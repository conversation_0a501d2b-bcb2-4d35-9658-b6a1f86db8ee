import { RiArrowLeftCircleLine } from "@remixicon/react";
import { IMAGE_PATH } from "globals";
import { useEffect } from "react";
import { Col, Container, Image, Row } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import "./styles.scss";

interface StaticPageProps {
  title: string;
  policyId: string;
  backPath?: string;
}

const StaticPage = ({
  title,
  policyId,
  backPath = ROUTE_PATH.SIGNUP,
}: StaticPageProps) => {
  const navigate = useNavigate();

  useEffect(() => {
    const scriptId = "termly-jssdk";
    if (!document.getElementById(scriptId)) {
      const script = document.createElement("script");
      script.id = scriptId;
      script.src = "https://app.termly.io/embed-policy.min.js";
      script.async = true;
      document.body.appendChild(script);

      return () => {
        document.body.removeChild(script);
      };
    }
  }, []);

  return (
    <div className="static-page">
      <span
        className="back-button position-fixed z-2 font-blue fw-bold cursor-pointer"
        onClick={() => navigate(backPath)}
      >
        <RiArrowLeftCircleLine className="me-2 align-text-top" />
        Back
      </span>

      <div className="static-page-logo text-center position-sticky top-0">
        <Image
          src={IMAGE_PATH.websiteLogo}
          className="object-fit-contain cursor-pointer"
          onClick={() => navigate(ROUTE_PATH.HOME)}
        />
      </div>

      <div className="static-page-content">
        <Container fluid>
          <Row className="justify-content-center align-items-stretch">
            <Col lg="11">
              <h1 className="text-center fw-bold position-sticky bg-light">
                <span className="mx-auto">{title}</span>
              </h1>
              <div name="termly-embed" data-id={policyId}></div>
            </Col>
          </Row>
        </Container>
      </div>
    </div>
  );
};

export default StaticPage;
