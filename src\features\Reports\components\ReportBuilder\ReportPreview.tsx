import { Ri<PERSON>loseLine } from "@remixicon/react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "react-bootstrap";
import useReportStore from "features/Reports/store/report";
import { generateHTML } from "@tiptap/html";
import StarterKit from "@tiptap/starter-kit";
import Highlight from "@tiptap/extension-highlight";
import Underline from "@tiptap/extension-underline";
import Table from "@tiptap/extension-table";
import TableCell from "@tiptap/extension-table-cell";
import TableHeader from "@tiptap/extension-table-header";
import TableRow from "@tiptap/extension-table-row";
import Image from "@tiptap/extension-image";
import TextAlign from "@tiptap/extension-text-align";
import ContentLock from "components/Common/CustomTiptapEditor/extensions/ContentLock";
import GraphPlaceholder from "components/Common/CustomTiptapEditor/extensions/GraphPlaceholder";

// Extensions array matching the editor
const extensions = [
  StarterKit,
  Highlight,
  Underline,
  Table.configure({
    resizable: true,
  }),
  <PERSON><PERSON>ow,
  TableHeader,
  TableCell,
  Image.configure({
    inline: true,
    allowBase64: true,
  }),
  GraphPlaceholder,
  ContentLock,
  TextAlign.configure({
    types: ["heading", "paragraph"],
  }),
];

// Function to handle content display (HTML or legacy JSON)
const getContentHtml = (content: string): string => {
  if (!content) return "<p><em>No content added yet.</em></p>";

  // Check if content is JSON (for backward compatibility with existing data)
  try {
    const parsedContent = JSON.parse(content);
    // If it's valid JSON, convert it to HTML
    return generateHTML(parsedContent, extensions);
  } catch (error) {
    // If it's not valid JSON, treat it as HTML (new format)
    return content || "<p><em>No content added yet.</em></p>";
  }
};

interface ReportPreviewProps {
  show: boolean;
  onClose: () => void;
}

const ReportPreview = ({ show, onClose }: ReportPreviewProps) => {
  const reportInfo = useReportStore((state) => state.reportInfo);

  const handleClose = () => {
    onClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleClose}
      size="lg"
      centered
      className="report-preview-modal"
    >
      <Modal.Header className="border-0 pb-0">
        <Modal.Title className="w-100 text-center">
          <h3 className="mb-0 fw-bold text-primary">Report Preview</h3>
        </Modal.Title>
        <Button
          variant="link"
          className="text-decoration-none position-absolute end-0 top-0 mt-2 me-2"
          onClick={handleClose}
        >
          <RiCloseLine size={24} />
        </Button>
      </Modal.Header>

      <Modal.Body className="pt-2">
        <div className="report-preview-content">
          {reportInfo.title && (
            <div className="preview-title mb-4 text-center">
              <h1 className="fw-bold text-primary">{reportInfo.title}</h1>
              <hr />
            </div>
          )}

          <div className="preview-sections">
            {reportInfo.sections.map((block, index) => (
              <div key={index} className="preview-section mb-4">
                <h3 className="section-title fw-bold text-secondary mb-3">
                  {block.title}
                </h3>
                <div
                  className="section-content"
                  dangerouslySetInnerHTML={{
                    __html: getContentHtml(block.content),
                  }}
                />
                {index < reportInfo.sections.length - 1 && (
                  <hr className="my-4" />
                )}
              </div>
            ))}
          </div>

          {reportInfo.sections.length === 0 && (
            <div className="text-center text-muted py-5">
              <p>No sections added to the report yet.</p>
            </div>
          )}
        </div>
      </Modal.Body>

      <Modal.Footer className="border-0 pt-0">
        <Button variant="secondary" onClick={handleClose}>
          Close Preview
        </Button>
      </Modal.Footer>
    </Modal>
  );
};

export default ReportPreview;
