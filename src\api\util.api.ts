import { useMutation, useQuery } from "@tanstack/react-query";
import { apiClient } from "./apiClient";
import { API_ENDPOINTS } from "globals";
import axios from "axios";

export const useContactUsMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(API_ENDPOINTS.CONTACT_US, payload);
      return response;
    },
  });

export const useGetStaticPage = (params: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_STATIC_PAGE, {
        params,
      });
      return response?.data;
    },
    queryKey: ["static-page"],
  });

export const useGetChatQuota = () =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_CHAT_QUOTA);
      return response?.data;
    },
    queryKey: ["chat-quota"],
    refetchOnWindowFocus: false,
  });

export const useGetS3PresignedUrlMutation = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.GET_S3_PRE_SIGNED_URL,
        payload,
      );
      return response;
    },
  });

export const useUploadFileToS3Mutation = () =>
  useMutation({
    mutationFn: async ({ url, fields, setUploadProgress }: any) => {
      const body = new FormData();
      Object.entries(fields).forEach(([key, value]: any) => {
        if (key === "ContentType") return;
        body.append(key, value);
      });

      const response = await axios.post(url, body, {
        onUploadProgress: (progressEvent: any) => {
          setUploadProgress((progressEvent.progress * 100).toFixed(1));
        },
      });
      return response;
    },
  });

export const useSaveDocumentSettings = () =>
  useMutation({
    mutationFn: async (payload: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.SAVE_DOCUMENT_SETTINGS,
        payload,
      );
      return response;
    },
  });

export const useGetDocumentSettings = (options = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.GET_DOCUMENT_SETTINGS);
      return response?.data;
    },
    queryKey: ["document-settings"],
    ...options,
  });

export const useDeleteExportLogo = () =>
  useMutation({
    mutationFn: async ({ orgId }: any) => {
      const response = await apiClient.delete(
        orgId
          ? API_ENDPOINTS.DELETE_ORG_EXPORT_LOGO(orgId)
          : API_ENDPOINTS.DELETE_EXPORT_LOGO,
      );
      return response;
    },
  });

export const useResetUserPasswordMutation = () =>
  useMutation({
    mutationFn: async ({ user_id, payload }: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.RESET_USER_PASSWORD(user_id),
        payload,
      );
      return response;
    },
  });
