import { RiMore2Fill } from "@remixicon/react";
import { useReSendInvite } from "api";
import {
  CustomDropdown,
  OfflineWordCountModal,
  OnlineWordCountModal,
} from "components";
import { OrgUserStatus } from "globals";
import { useHandleMemberStatus } from "hooks";
import { useState } from "react";
import toast from "react-hot-toast";

const ORGANISATION_MEMBERS_QUERY_KEY = "organisation-members";
const ORGANISATION_TEAM_PLAN_QUERY_KEY = "organisation-team-plan";

interface ActionsCellProps {
  row: any;
  organization: any;
  checkSameUser: (id: number | string) => boolean;
  wordCountConfig: {
    word_limit: number;
    assigned_words: number;
    word_limit_setting: any;
  };
  isOfflineAccount: boolean;
}

enum ActionOptions {
  CANCEL = "cancel",
  BLOCK = "block",
  WORD_COUNT = "word_count",
  RESEND_INVITE = "re_send_invite",
}

export default function ActionsCell({
  row,
  organization,
  checkSameUser,
  wordCountConfig,
  isOfflineAccount,
}: ActionsCellProps) {
  const [showUpgradeWordCountModal, setShowUpgradeWordCountModal] =
    useState(false);

  const { onClickItem: onClickStatus } = useHandleMemberStatus({
    queryKeys: [
      ORGANISATION_MEMBERS_QUERY_KEY,
      ORGANISATION_TEAM_PLAN_QUERY_KEY,
    ],
  });
  const { mutateAsync: ReSendInvite } = useReSendInvite();

  const onClickInvite = async () => {
    try {
      const result: any = await ReSendInvite({
        member_id: row?.member_id,
        org_id: organization?.id,
      });
      if (result?.success) {
        toast.success(result?.message);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleSelect = (value: any) => {
    if (
      checkSameUser(row?.member_id) &&
      ![ActionOptions.WORD_COUNT].includes(value)
    ) {
      toast.error("You don't have permission to perform this action");
      return;
    }
    switch (value) {
      case ActionOptions.CANCEL:
        onClickStatus({
          row,
          organization,
          status: OrgUserStatus.CANCEL,
        });
        break;
      case ActionOptions.BLOCK:
        if (row?.status === OrgUserStatus.PENDING) {
          toast.error(
            "Wait for the user to accept the invite before managing their status.",
          );
          return;
        }

        onClickStatus({
          row,
          organization,
          status:
            row?.status === OrgUserStatus.BLOCK
              ? OrgUserStatus.ACTIVE
              : OrgUserStatus.BLOCK,
        });
        break;
      case ActionOptions.WORD_COUNT:
        setShowUpgradeWordCountModal(true);
        break;
      case ActionOptions.RESEND_INVITE:
        onClickInvite();
        break;
    }
  };

  const getDropdownItems = (row: any) => {
    const items = [
      {
        label: <span>Cancel User</span>,
        value: ActionOptions.CANCEL,
      },
      {
        label: (
          <span>
            {row?.status === OrgUserStatus.BLOCK
              ? "Reinstate User"
              : "Block User"}
          </span>
        ),
        value: ActionOptions.BLOCK,
      },
      {
        label: (
          <span>{isOfflineAccount ? "Amend" : "Purchase"} Word Limit</span>
        ),
        value: ActionOptions.WORD_COUNT,
      },
    ];

    if (row?.status === OrgUserStatus.PENDING) {
      items.push({
        label: <span>Re-send invite</span>,
        value: ActionOptions.RESEND_INVITE,
      });
    }

    return items;
  };

  const items = getDropdownItems(row);

  const onCloseWordCountModal = () => {
    setShowUpgradeWordCountModal(false);
  };

  return (
    <>
      {row?.status !== OrgUserStatus.CANCEL ? (
        <CustomDropdown
          title={<RiMore2Fill size={18} />}
          items={items}
          className="table-actions"
          onSelect={handleSelect}
          preserveTitle
        />
      ) : (
        <span className="fw-bold text-danger">N/A</span>
      )}

      {showUpgradeWordCountModal &&
        (isOfflineAccount ? (
          <OfflineWordCountModal
            show={showUpgradeWordCountModal}
            onClose={onCloseWordCountModal}
            wordCountConfig={wordCountConfig}
            row={row}
            organization={organization}
          />
        ) : (
          <OnlineWordCountModal
            show={showUpgradeWordCountModal}
            onClose={onCloseWordCountModal}
            wordCountConfig={wordCountConfig}
            isOfflineAccount={isOfflineAccount}
            row={row}
          />
        ))}
    </>
  );
}
