@use "./variables" as *;
@use "./mixins/mixins.scss" as *;

.after-none::after,
.before-none::before {
  display: none !important;
}

.cursor-pointer {
  cursor: pointer !important;
}

.default-padding {
  padding: 60px 0px 90px 0px;

  @media only screen and (max-width: 991px) {
    padding: 30px 0px 30px 0px;
  }
}

.fw-500 {
  font-weight: 500 !important;
}

.font-primary {
  color: $primary-color !important;
}

.font-gray {
  color: $color-gray !important;
}

.font-light {
  color: $color-light !important;
}

.font-brown {
  color: $color-brown !important;
}

.bg-brown {
  background-color: $color-brown !important;
}

.border-brown {
  border-color: $color-brown !important;
}

.bg-blue {
  background-color: $primary-color !important;
}

.border-blue {
  border-color: $primary-color !important;
}

.truncate-by-line {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  line-clamp: 1;
  -webkit-line-clamp: 1;
}

.main-heading {
  font-size: 64px;
  font-weight: 900;
  letter-spacing: 1px;
  color: #f9f9f9;
  text-transform: uppercase;

  @media only screen and (min-width: 991px) and (max-width: 1399px) {
    font-size: 50px;
  }

  @media only screen and (max-width: 767px) {
    font-size: 30px;
  }

  @media only screen and (max-width: 576px) {
    font-size: 28px;
  }
}

.main-sub-heading {
  color: #f9f9f9;
  font-size: 40px;
  font-weight: 600;

  @media only screen and (min-width: 991px) and (max-width: 1399px) {
    font-size: 30px;
  }

  @media only screen and (max-width: 767px) {
    font-size: 20px;
  }
}

.main-description {
  color: #f9f9f9;
  font-size: 30px;

  @media only screen and (min-width: 991px) and (max-width: 1399px) {
    font-size: 22px;
  }

  @media only screen and (max-width: 767px) {
    font-size: 18px;
  }
}

a {
  text-decoration: none;
}

.custom-dropdown-select {
  button {
    text-align: start;
    color: #70828d;

    &:hover,
    &:active,
    &:focus,
    &.show,
    &:first-child:active {
      color: #70828d;
    }

    &::after {
      content: " ";
      position: absolute;
      width: 20px;
      height: 20px;
      background-image: url("/src/assets/images/downArrow.svg");
      background-repeat: no-repeat;
      background-position: center center;
      background-size: 15px;
      border: none;
      top: 50%;
      transform: translateY(-50%);
      right: 20px;
    }

    .select-value {
      opacity: 0.7;
    }
  }

  .dropdown {
    &-menu {
      width: 100%;
      background-color: #ffffff;
      border: 1px solid #e1e0de;
      font-size: 16px;
      border-radius: 12px;
      padding: 12px;
      max-height: 200px;
      overflow-x: hidden;
      overflow-y: auto;

      @include slim-scrollbar;

      @media only screen and (max-width: 576px) {
        font-size: 15px;
        padding: 10px 0px;
      }
    }

    &-item {
      background-color: transparent;
      color: #0d3149;

      &:hover {
        background-color: #ad986f29;
      }

      &:not(:last-child) {
        margin-bottom: 5px !important;
      }
    }
  }
}
