import { RiCustomerService2Fill } from "@remixicon/react";
import { useCheckUserSubscription } from "api";
import { getSettingsCardsConfig, IMAGE_PATH } from "globals";
import { useOrganisationEnable, useUserRoles, useUserStatus } from "hooks";
import { useEffect } from "react";
import { Button, Container, Image, Row } from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setSubscriptionInfo } from "stores";
import SettingCard from "./SettingCard";
import "./styles.scss";
import useUserStore from "stores/user";

const Settings = () => {
  const navigate = useNavigate();
  const userRole = useUserRoles();
  const { data: subscriptionData = {} } = useCheckUserSubscription();
  const isOrganisationEnabled = useOrganisationEnable();
  const { isIndividualUser, isOfflineAccount } = useUserStatus();
  const userDetails = useUserStore((state) => state.userInfo.user);
  const termsSheetURL = userDetails?.terms_sheet_url;

  useEffect(() => {
    if (subscriptionData?.subscription) {
      setSubscriptionInfo(subscriptionData?.subscription);
    }
  }, [subscriptionData?.subscription]);

  const SETTINGS_CARDS_CONFIG = getSettingsCardsConfig({
    IMAGE_PATH,
    ROUTE_PATH,
    isOrganisationEnabled,
    isIndividualUser,
    isOfflineAccount,
    termsSheetURL,
  }).filter((item) => item?.user_role?.includes(userRole) && !item?.disabled);

  return (
    <main className="profile-section d-flex bg-white flex-column align-items-stretch w-100">
      <div className="breadcrumb-wrapper d-flex flex-lg-row align-items-center justify-content-between">
        <div className="page-details d-flex align-items-lg-center justify-content-start">
          <Image
            src={IMAGE_PATH.settingPageImg}
            alt="settings"
            className="page-details-img object-fit-contain"
          />

          <div className="page-details-page-name">
            <h3 className="mb-lg-2 mb-1 fw-bold">Settings</h3>
            <p className="mb-0">
              You can edit your
              <br className="d-sm-none d-block" />
              <span className="ms-sm-1">informations.</span>
            </p>
          </div>
        </div>

        <div>
          <span className="fw-bold me-2">Support</span>

          <Button
            onClick={() => navigate(ROUTE_PATH.CONTACT_US)}
            className="bg-blue border-blue p-0"
          >
            <RiCustomerService2Fill size={"24px"} color="#F9F9F9" />
          </Button>
        </div>
      </div>

      <hr className="m-0 border-0" />

      <div className="profile-section-action-card-list">
        <Container fluid>
          <Row className="gy-4">
            {SETTINGS_CARDS_CONFIG.map((cardItem, idx) => {
              return (
                <SettingCard
                  key={`setting-card-${idx}`}
                  cardItem={cardItem}
                  additionalClasses={{
                    col: "profile-section-action-card-list-col",
                  }}
                />
              );
            })}
          </Row>
        </Container>
      </div>
    </main>
  );
};

export default Settings;
