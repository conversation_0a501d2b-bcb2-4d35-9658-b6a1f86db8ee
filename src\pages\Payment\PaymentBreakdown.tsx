import { FC } from "react";
import { getStripeFormateDate } from "utils";
import {
  MonthlyCostTable,
  PaymentBreakdownTable,
  PaymentItemInterface,
  PlanInfo,
} from "./BreakdownComponents";

interface PaymentBreakdownDetailsProps {
  paymentItems?: PaymentItemInterface[];
  planInfo?: PlanInfo;
}

const PaymentBreakdownDetails: FC<PaymentBreakdownDetailsProps> = ({
  paymentItems = [],
  planInfo,
}) => {
  const formatDate = getStripeFormateDate;

  return (
    <div className="payment-details mt-4 d-flex flex-column gap-4">
      {planInfo?.monthly_cost && planInfo.monthly_cost.length > 0 && (
        <MonthlyCostTable monthlyCost={planInfo.monthly_cost} />
      )}
      <PaymentBreakdownTable
        paymentItems={paymentItems}
        planInfo={planInfo}
        formatDate={formatDate}
      />
    </div>
  );
};

export default PaymentBreakdownDetails;
