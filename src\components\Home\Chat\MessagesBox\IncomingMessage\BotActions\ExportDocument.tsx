import { useState } from "react";
import {
  RiArticleLine,
  RiFileTextLine,
  RiSparkling2Line,
} from "@remixicon/react";
import { CustomDropdown } from "components/FormFields";
import { marked } from "marked";
import { Spinner } from "react-bootstrap";
import { useExportDocumentMutation } from "api";
import "./styles.scss";

const downloadBlob = (blob: Blob, filename: string) => {
  const href = URL.createObjectURL(blob);
  const link = document.createElement("a");
  link.href = href;
  link.setAttribute("download", filename);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(href);
};

const ExportDocument = ({
  messageItem,
}: {
  messageItem: Record<string, any>;
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const { mutateAsync: exportDocumentMutation } = useExportDocumentMutation();
  const htmlContent = messageItem?.reid_text
    ? marked(messageItem.reid_text)
    : "";

  const handleExport = async (value: string) => {
    const with_branding = value === "true";
    setIsExporting(true);

    try {
      const blob = await exportDocumentMutation({
        payload: {
          with_branding,
          content: htmlContent,
        },
      });
      downloadBlob(blob, "chatredact-response.docx");
    } catch (error: any) {
      console.error("Export failed:", error.message);
    } finally {
      setIsExporting(false);
    }
  };

  return isExporting ? (
    <Spinner size="sm" />
  ) : (
    <CustomDropdown
      title={<RiArticleLine size={18} />}
      items={[
        {
          label: (
            <span>
              <RiSparkling2Line size={18} className="me-2" />
              Export With Branding
            </span>
          ),
          value: "true",
        },
        {
          label: (
            <span>
              <RiFileTextLine size={18} className="me-2" />
              Export As Plain File
            </span>
          ),
          value: "false",
        },
      ]}
      className="export-document"
      onSelect={handleExport}
      preserveTitle
    />
  );
};

export default ExportDocument;
