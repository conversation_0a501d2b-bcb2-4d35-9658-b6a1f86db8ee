import { Card, Col, Image } from "react-bootstrap";
import { Link } from "react-router-dom";

export default function SettingCard({ cardItem, additionalClasses }: any) {
  const {
    icon,
    headingAlt,
    path,
    heading,
    subheading,
    subheadingIcon,
    subheadingAlt,
    customStyle = {},
    onClick,
  } = cardItem ?? {};
  return (
    <Col lg="6" md="6" xl="6" xxl="3" className={additionalClasses?.col}>
      <Card
        as={!onClick ? Link : undefined}
        to={path}
        className="profile-section-action-card-list-item justify-content-center aligh-items-center cursor-pointer"
        onClick={onClick}
      >
        <Card.Body className="d-flex gap-3 flex-column align-items-center justify-content-center text-center">
          <Image
            src={icon}
            alt={headingAlt}
            className="action-img object-fit-contain"
            style={customStyle}
          />

          <h3 className="mb-0 heading">{heading}</h3>

          <p className="mb-0 sub-heading">
            {subheading}
            {subheadingIcon && (
              <Image
                src={subheadingIcon}
                alt={subheadingAlt}
                className="inter-img ms-2"
              />
            )}
          </p>
        </Card.Body>
      </Card>
    </Col>
  );
}
