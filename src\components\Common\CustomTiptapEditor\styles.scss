// .editor-box {
//   .tiptap {
//     width: 100%;
//     min-height: 150px;
//     padding: 0.75rem;
//     border-radius: 4px;
//     border: 1px solid #ccc;
//     background-color: #fff;
//     resize: vertical;

//     ::selection {
//       background-color: #ad986f;
//       color: #000;
//     }

//     ::-moz-selection {
//       background-color: rgb(173, 152, 111);
//       color: #000;
//     }

//     .content-locked {
//       background-color: rgba(173, 152, 111, 0.15);
//       border-radius: 3px;
//       padding: 1px 3px;
//       position: relative;

//       &::before {
//         content: "🔒";
//         font-size: 0.7em;
//         margin-right: 2px;
//         opacity: 0.7;
//       }

//       &:hover {
//         background-color: rgba(173, 152, 111, 0.25);
//       }
//     }

//     table {
//       border-collapse: collapse;
//       margin: 0;
//       overflow: hidden;
//       table-layout: fixed;
//       width: 100%;
//       transition: all 0.2s ease;
//       position: relative;

//       &:hover {
//         box-shadow: 0 2px 8px rgba(173, 152, 111, 0.15);
//         border-radius: 4px;
//       }

//       td,
//       th {
//         border: 2px solid #ced4da;
//         box-sizing: border-box;
//         min-width: 1em;
//         padding: 3px 5px;
//         position: relative;
//         vertical-align: top;

//         > * {
//           margin-bottom: 0;
//         }
//       }

//       th {
//         background-color: #f8f9fa;
//         font-weight: bold;
//       }

//       .selectedCell:after {
//         background: rgba(200, 200, 255, 0.4);
//         content: "";
//         left: 0;
//         right: 0;
//         top: 0;
//         bottom: 0;
//         pointer-events: none;
//         position: absolute;
//         z-index: 2;
//       }

//       .column-resize-handle {
//         background-color: #adf;
//         bottom: -2px;
//         position: absolute;
//         right: -2px;
//         top: 0;
//         width: 4px;
//       }
//     }

//     .graph-placeholder {
//       border: 2px dashed #ad986f;
//       border-radius: 8px;
//       padding: 2rem;
//       margin: 1rem 0;
//       text-align: center;
//       background-color: #f8f9fa;
//       cursor: pointer;
//       transition: all 0.2s ease;

//       &:hover {
//         background-color: #e9ecef;
//         border-color: #8b7355;
//       }

//       .graph-placeholder-content {
//         display: flex;
//         flex-direction: column;
//         align-items: center;
//         gap: 0.5rem;
//       }

//       .graph-placeholder-icon {
//         font-size: 2rem;
//       }

//       .graph-placeholder-text {
//         font-weight: 600;
//         color: #ad986f;
//       }

//       .graph-placeholder-subtitle {
//         font-size: 0.875rem;
//         color: #6c757d;
//       }
//     }

//     img {
//       max-width: 100%;
//       height: auto;
//       border-radius: 4px;
//     }
//   }
// }

.custom-editor {
  .editor-content {
    .ProseMirror {
      outline: none;
      padding: 1rem;
      min-height: 200px;
      border-radius: 4px;
      // border: 1px solid #e9ecef;
      background-color: #ffffff;
      font-size: 16px;
      line-height: 1.6;
      color: #333;

      // Headings
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin-top: 1.5rem;
        margin-bottom: 0.75rem;
        font-weight: 600;
        line-height: 1.3;
        color: #2c3e50;

        &:first-child {
          margin-top: 0;
        }
      }

      h1 {
        font-size: 2rem;
        // border-bottom: 2px solid #ad986f;
        padding-bottom: 0.5rem;
      }

      h2 {
        font-size: 1.5rem;
        color: #ad986f;
      }

      h3 {
        font-size: 1.25rem;
      }

      // Paragraphs
      p {
        margin-bottom: 1rem;

        &:last-child {
          margin-bottom: 0;
        }

        &.is-empty::before {
          content: attr(data-placeholder);
          color: #adb5bd;
          pointer-events: none;
          height: 0;
          float: left;
        }
      }

      // Lists
      ul,
      ol {
        margin: 1rem 0;
        padding-left: 2rem;

        li {
          margin-bottom: 0.5rem;

          p {
            margin: 0;
          }
        }
      }

      ul {
        list-style-type: disc;
      }

      ol {
        list-style-type: decimal;
      }

      // Blockquotes
      blockquote {
        border-left: 4px solid #ad986f;
        margin: 1.5rem 0;
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        font-style: italic;
        color: #6c757d;

        p {
          margin: 0;
        }
      }

      // Code
      code {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 0.2rem 0.4rem;
        font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
        font-size: 0.875rem;
        color: #e83e8c;
      }

      pre {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 1rem;
        margin: 1rem 0;
        overflow-x: auto;

        code {
          background: none;
          border: none;
          padding: 0;
          color: #333;
          font-size: 0.875rem;
        }
      }

      table {
        border-collapse: collapse;
        margin: 0;
        overflow: hidden;
        table-layout: fixed;
        width: 100%;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          box-shadow: 0 2px 8px rgba(173, 152, 111, 0.15);
          border-radius: 4px;
        }

        td,
        th {
          border: 2px solid #ced4da;
          box-sizing: border-box;
          min-width: 1em;
          padding: 3px 5px;
          position: relative;
          vertical-align: top;

          > * {
            margin-bottom: 0;
          }
        }

        th {
          background-color: #f8f9fa;
          font-weight: bold;
        }

        .selectedCell:after {
          background: rgba(200, 200, 255, 0.4);
          content: "";
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          pointer-events: none;
          position: absolute;
          z-index: 2;
        }

        .column-resize-handle {
          background-color: #adf;
          bottom: -2px;
          position: absolute;
          right: -2px;
          top: 0;
          width: 4px;
        }
      }

      // Images
      img {
        max-width: 100%;
        height: auto;
        border-radius: 4px;
        margin: 1rem 0;
      }

      // Horizontal rule
      hr {
        border: none;
        border-top: 2px solid #dee2e6;
        margin: 2rem 0;
      }

      // Text formatting
      strong {
        font-weight: 600;
      }

      em {
        font-style: italic;
      }

      u {
        text-decoration: underline;
      }

      s {
        text-decoration: line-through;
      }

      // Highlight
      mark {
        background-color: #fff3cd;
        padding: 0.1rem 0.2rem;
        border-radius: 2px;
      }

      // Content lock styling
      [data-locked="true"] {
        background-color: rgba(173, 152, 111, 0.1);
        border-radius: 3px;
        padding: 0.1rem 0.2rem;
        position: relative;

        &::after {
          content: "🔒";
          font-size: 0.75rem;
          margin-left: 0.25rem;
          opacity: 0.7;
        }

        &:hover {
          background-color: rgba(173, 152, 111, 0.2);
        }
      }

      // Graph placeholder
      .graph-placeholder {
        border: 2px dashed #ad986f;
        border-radius: 8px;
        padding: 2rem;
        margin: 1rem 0;
        text-align: center;
        background-color: #f8f9fa;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background-color: rgba(173, 152, 111, 0.1);
          border-color: #8b7355;
        }

        .graph-placeholder-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.5rem;
        }

        .graph-placeholder-icon {
          font-size: 2rem;
        }

        .graph-placeholder-text {
          font-weight: 600;
          color: #ad986f;
        }

        .graph-placeholder-subtitle {
          font-size: 0.875rem;
          color: #6c757d;
        }
      }

      // Selection styling
      ::selection {
        background-color: #ad986f;
        color: #000;
      }

      ::-moz-selection {
        background-color: rgb(173, 152, 111);
        color: #000;
      }
    }
  }
}
