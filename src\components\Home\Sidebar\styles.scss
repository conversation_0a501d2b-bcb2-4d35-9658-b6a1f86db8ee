@use "/src/styles/mixins/mixins.scss" as mixins;

.sidebar {
  width: 130px;
  border-radius: 12px;
  padding: 40px 0px 0px 0px;
  gap: 40px;

  &-logo {
    width: 100%;
    height: 80px;

    & + hr {
      background: linear-gradient(
        90deg,
        rgba(255, 255, 255, 0) 0%,
        #fff 49.5%,
        rgba(255, 255, 255, 0) 100%
      );

      height: 3px;
    }

    img {
      height: 85% !important;
    }
  }

  &-navigation {
    gap: 15px;
    height: calc(100vh - 370px);
    overflow-x: hidden;
    overflow-y: auto;

    @include mixins.slim-scrollbar;

    &::-webkit-scrollbar {
      display: none;
    }

    &-item {
      a {
        color: #f9f9f9;
        padding: 15px 0px;
        line-height: 1;
        gap: 15px;
        font-weight: 500;

        &::after {
          content: " ";
          position: absolute;
          top: 0;
          right: 0;
          bottom: 0;
          height: 100%;
          width: 6px;
          background-color: #ad986f;
          opacity: 0;
        }

        &:hover {
          color: #ad986f;
        }

        &.item-active::after {
          opacity: 1;
        }

        &.item-active {
          color: #ad986f;
          font-weight: bold !important;
        }
      }
    }
  }

  &-footer-img {
    padding: 0px 10px 10px 10px;
    font-size: 10px;
    font-weight: 500;
  }
}
