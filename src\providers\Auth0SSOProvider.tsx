import { Auth0Provider } from "@auth0/auth0-react";
import { PropsWithChildren } from "react";
import { getAppOriginURL } from "utils";

export const Auth0SSOProvider = ({ children }: PropsWithChildren) => {
  return (
    <Auth0Provider
      domain={import.meta.env.VITE_AUTH0_DOMAIN}
      clientId={import.meta.env.VITE_AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri:
          import.meta.env.VITE_AUTH0_LOGIN_REDIRECT_URI ||
          getAppOriginURL() ||
          window.location.origin,
      }}
    >
      {children}
    </Auth0Provider>
  );
};
