import BadResponse from "./BadResponse";
import CopyToClipBoard from "./CopyToClipBoard";
import ExportDocument from "./ExportDocument";
import ReadAloud from "./ReadAloud";
import Regenerate from "./Regenerate";
import "./styles.scss";

const BotActions = ({
  messageItem,
  isLastMessage,
  audioRef,
  history,
  setHistory,
  isPlaying,
  setIsPlaying,
}: Record<string, any>) => {
  return (
    <div className="chat-action-buttons d-flex align-items-center justify-content-start align-self-start">
      <ReadAloud
        messageItem={messageItem}
        audioRef={audioRef}
        history={history}
        setHistory={setHistory}
        isPlaying={isPlaying}
        setIsPlaying={setIsPlaying}
      />

      <CopyToClipBoard messageItem={messageItem} />

      <BadResponse messageItem={messageItem} />

      {isLastMessage && <Regenerate messageItem={messageItem} />}

      <ExportDocument messageItem={messageItem} />
    </div>
  );
};

export default BotActions;
