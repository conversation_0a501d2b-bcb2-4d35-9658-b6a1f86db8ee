import { ErrorMessage } from "formik";
import React, { useState } from "react";
import { Dropdown, DropdownButton } from "react-bootstrap";
import "./styles.scss";

export interface DropdownItem {
  label: string | React.ReactNode;
  value: string;
  disabled?: boolean;
}

export interface CustomDropdownProps {
  label?: string;
  title?: string | React.ReactNode;
  items: DropdownItem[];
  onSelect?: (value: string) => void;
  variant?: string;
  className?: string;
  defaultSelected?: DropdownItem;
  name?: string;
  value?: string;
  preserveTitle?: boolean;
}

const CustomDropdown: React.FC<CustomDropdownProps> = ({
  label,
  title = "Select an option",
  items,
  onSelect,
  variant = "outline-secondary",
  className = "",
  defaultSelected,
  name,
  value,
  preserveTitle = false,
}) => {
  const [selected, setSelected] = useState<DropdownItem | null>(
    defaultSelected || null,
  );

  const computedSelected = value
    ? items.find((item) => item.value === value)
    : selected;

  const handleSelect = (eventKey: string | null) => {
    const selectedItem = items.find((item) => item.value === eventKey);
    if (selectedItem) {
      if (value === undefined) {
        setSelected(selectedItem);
      }
      onSelect && onSelect(selectedItem.value);
    }
  };

  return (
    <div
      className={`custom-dropdown form-group m-0 p-0 position-relative ${className}`}
    >
      {label && (
        <div className="d-flex justify-content-between align-items-center">
          <label className="form-label">{label}</label>
        </div>
      )}
      <DropdownButton
        id="custom-dropdown"
        title={
          preserveTitle
            ? title
            : computedSelected
              ? computedSelected.label
              : title
        }
        variant={variant}
        className="w-100"
        onSelect={handleSelect}
      >
        {items.map((item) => (
          <Dropdown.Item
            key={item.value}
            eventKey={item.value}
            disabled={item?.disabled}
          >
            {item.label}
          </Dropdown.Item>
        ))}
      </DropdownButton>
      {name && <ErrorMessage component={"span"} name={name} />}
    </div>
  );
};

export default CustomDropdown;
