import useReportStore from "features/Reports/store/report";
import HeaderBar from "./HeaderBar";
import SectionBlock from "./SectionBlock";
import "./styles.scss";

const ReportBuilder = () => {
  const reportInfo = useReportStore((state) => state.reportInfo);
  const { title, sections } = reportInfo || {};

  return (
    <div className="report-builder-section bg-white h-100 w-100 rounded p-3 overflow-auto">
      <HeaderBar title={title} />
      {sections.map((block, idx) => (
        <SectionBlock key={idx} {...block} index={idx} />
      ))}
    </div>
  );
};

export default ReportBuilder;
