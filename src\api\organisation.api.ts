import { useMutation, useQuery } from "@tanstack/react-query";
import { API_ENDPOINTS } from "globals";
import { apiClient } from "./apiClient";

export const useInviteNewMutation = () => {
  return useMutation({
    mutationFn: ({ id, payload }: any) => {
      return apiClient.post(API_ENDPOINTS.INVITE_NEW(id), payload);
    },
  });
};

export const useGetOrganisation = (options: { staleTime?: number } = {}) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(API_ENDPOINTS.ORGANISATION);
      return response?.data;
    },
    queryKey: ["organisation"],
    staleTime: Infinity,
    ...options,
  });

export const useCreateOrganisation = () => {
  return useMutation({
    mutationFn: (payload: Record<string, any>) => {
      return apiClient.post(API_ENDPOINTS.ORGANISATION, payload);
    },
  });
};

export const useUpdateOrganisation = () => {
  return useMutation({
    mutationFn: ({ id, payload }: any) => {
      return apiClient.put(API_ENDPOINTS.UPDATE_ORGANISATION(id), payload);
    },
  });
};

export const useGetOrganisationMembers = ({ id, params }: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.ORGANISATION_MEMBERS(id),
        {
          params,
        },
      );
      return response?.data;
    },
    queryKey: ["organisation-members", id, ...Object.values(params)],
    enabled: !!id,
  });

export const useOrganisationTeamPlan = (id: any) =>
  useQuery({
    queryFn: async () => {
      const response = await apiClient.get(
        API_ENDPOINTS.ORGANISATION_TEAM_PLAN(id),
      );
      return response?.data;
    },
    queryKey: ["organisation-team-plan", id],
    enabled: !!id,
  });

export const useUpdateOrgPromptMutation = () =>
  useMutation({
    mutationFn: async ({ org_id, payload }: any) => {
      const response = await apiClient.put(
        API_ENDPOINTS.UPDATE_ORG_PROMPT(org_id),
        payload,
      );
      return response;
    },
  });

export const useDeleteOrgPromptMutation = () =>
  useMutation({
    mutationFn: async ({ org_id, prompt_id }: any) => {
      const response = await apiClient.delete(
        API_ENDPOINTS.DELETE_ORG_PROMPT({ org_id, prompt_id }),
      );
      return response;
    },
  });

export const useSaveOrgDocumentSettings = () =>
  useMutation({
    mutationFn: async ({ payload, orgId }: any) => {
      const response = await apiClient.post(
        API_ENDPOINTS.SAVE_ORG_DOCUMENT_SETTINGS(orgId),
        payload,
      );
      return response;
    },
  });

export const useUpgradeOrgMemberWordLimit = () =>
  useMutation({
    mutationFn: async ({ payload, org_id, member_id }: any) => {
      const response = await apiClient.put(
        API_ENDPOINTS.UPGRADE_ORG_MEMBER_WORD_LIMIT({ org_id, member_id }),
        payload,
      );
      return response;
    },
  });
