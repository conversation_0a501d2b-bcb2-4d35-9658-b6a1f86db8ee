import { RiCloseLine } from "@remixicon/react";
import { Button, Modal } from "react-bootstrap";
import <PERSON>actCrop, { centerCrop, makeAspectCrop } from "react-image-crop";
import "react-image-crop/dist/ReactCrop.css";
import "./styles.scss";

function centerAspectCrop(
  mediaWidth: number,
  mediaHeight: number,
  aspect: number,
) {
  return centerCrop(
    makeAspectCrop(
      {
        unit: "%",
        width: 30,
      },
      aspect,
      mediaWidth,
      mediaHeight,
    ),
    mediaWidth,
    mediaHeight,
  );
}

const ImageCropperModal = ({
  show,
  onClose,
  cropperConfig = {},
  onSave,
}: any) => {
  const {
    imgSrc,
    crop,
    setCrop,
    setCompletedCrop,
    aspect,
    imgRef,
    scale,
    rotate,
    minHeight,
    maxHeight,
    isCircularCrop,
    setScale,
    enableScale,
  } = cropperConfig || {};

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { width, height } = e.currentTarget;
    setCrop(centerAspectCrop(width, height, 1));
  };

  const handleScaleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(e.target.value);
    setScale(newScale);
  };

  return (
    <Modal
      show={show}
      onHide={onClose}
      keyboard={false}
      centered
      className="cropper-modal"
    >
      <Modal.Body className="d-flex justify-content-center align-items-center position-relative">
        <Button
          variant="link"
          className="text-decoration-none modal-close-button bg-brown rounded-circle position-absolute z-3 d-flex justify-content-center align-items-center"
          onClick={onClose}
        >
          <RiCloseLine size={"40px"} color="#f9f9f9" />
        </Button>

        <div
          className="auth-form d-flex justify-content-center align-items-center flex-column"
          style={{ gap: "30px" }}
        >
          <div className="d-flex flex-column" style={{ gap: "15px" }}>
            <h1 className="auth-form-heading text-uppercase mb-0 text-center lh-1">
              Crop Logo
            </h1>
          </div>
          {imgSrc && (
            <div
              className="crop-container"
              style={{
                maxWidth: "100%",
                overflow: "hidden",
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <ReactCrop
                crop={crop}
                onChange={(_, percentCrop) => setCrop(percentCrop)}
                onComplete={(c) => setCompletedCrop(c)}
                aspect={aspect}
                circularCrop={isCircularCrop}
                minHeight={minHeight}
                maxHeight={maxHeight}
              >
                <img
                  ref={imgRef}
                  alt="Crop preview"
                  src={imgSrc}
                  style={{
                    transform: `scale(${scale}) rotate(${rotate}deg)`,
                    transformOrigin: "center",
                    border: enableScale ? "none" : "1px dashed #ccc",
                    padding: "10px",
                    maxWidth: "100%",
                    height: "auto",
                    minHeight,
                    maxHeight,
                  }}
                  onLoad={onImageLoad}
                />
              </ReactCrop>
            </div>
          )}
          <div className="website-form d-flex flex-column gap-3">
            {enableScale && (
              <div className="d-flex flex-column align-items-center w-100">
                <label htmlFor="scaleRange" className="mb-2">
                  Zoom
                </label>
                <input
                  id="scaleRange"
                  type="range"
                  min="0"
                  max="3"
                  step="0.01"
                  value={scale}
                  onChange={handleScaleChange}
                  style={{ width: "100%" }}
                />
              </div>
            )}
            <div
              className="action-btns mt-3 d-flex flex-column"
              style={{ gap: "30px" }}
            >
              <Button
                type="submit"
                className="submit-btn w-100 bg-brown border-brown text-uppercase font-light"
                onClick={onSave}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      </Modal.Body>
    </Modal>
  );
};

export default ImageCropperModal;
