import { useEffect } from "react";
import { Button } from "react-bootstrap";
import { isMobileOnly } from "react-device-detect";
import { useParams, useSearchParams } from "react-router-dom";
import { useGetReportDetails } from "../api";
import { HistoryBox } from "../components";
import ReportBuilder from "../components/ReportBuilder";
import { resetReportState, setReportInfo } from "../store";
import "./styles.scss";

const Reports = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();

  const { data: reportDetails = {} } = useGetReportDetails(id);

  const showEditor = searchParams.get("edit") === "true" || !!id;

  useEffect(() => {
    if (reportDetails && Object.keys(reportDetails).length) {
      setReportInfo(reportDetails);
    }
  }, [reportDetails]);

  const handleBuildReportClick = () => {
    resetReportState();
    searchParams.set("edit", "true");
    setSearchParams(searchParams);
  };

  return (
    <main className="reports-section d-flex flex-lg-row flex-column align-items-stretch w-100">
      {!isMobileOnly && <HistoryBox handleBuildReportClick={handleBuildReportClick} />}
      <div className="report-content w-100">
        {!showEditor ? (
          <div className="bg-white d-flex flex-column justify-content-center align-items-center h-100 rounded">
            <h2>Welcome to the Reports</h2>
            <p>Click below to start building your report.</p>
            <Button
              variant="primary"
              className="py-2 bg-blue font-light border-blue text-decoration-none fw-bold d-flex justify-content-between align-items-center"
              onClick={handleBuildReportClick}
            >
              Build New Report
            </Button>
          </div>
        ) : (
          <ReportBuilder />
        )}
      </div>
    </main>
  );
};

export default Reports;
