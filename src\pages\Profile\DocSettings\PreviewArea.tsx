import { isMobileOnly } from "react-device-detect";
import LogoPicture from "../LogoPicture";

export default function PreviewArea({ values }: { values: any }) {
  return (
    <div className="preview-area">
      <div
        className="preview-content d-flex flex-column gap-4 p-5"
        style={{ fontFamily: values.font }}
      >
        <div className="d-flex align-items-center">
          <LogoPicture
            logo={values.primary_logo}
            imageProps={{
              style: {
                width: isMobileOnly ? 50 : 100,
                height: isMobileOnly ? 50 : 100,
              },
            }}
            imageClassName="border-0"
          />
          <div className="vr preview-header-vr"></div>
          {values.secondary_logo ? (
            <LogoPicture
              logo={values.secondary_logo}
              imageProps={{
                style: { maxWidth: isMobileOnly ? "150px" : "300px" },
                height: 100,
                className: "object-fit-contain",
              }}
            />
          ) : (
            <div className="fs-1 text-dark title-placeholder">
              INSERT TITLE HERE
            </div>
          )}
        </div>
        <hr
          className="m-0"
          style={{
            backgroundImage: "none",
            backgroundColor: "#000",
            height: "1px",
            opacity: 1,
            border: "none",
          }}
        />
        <div className="text-lines d-flex flex-column gap-3">
          {Array.from({ length: 7 }).map((_, index) => (
            <div key={index} className="line"></div>
          ))}
        </div>
      </div>
    </div>
  );
}
