import { GenerateReport, REPORTS } from "features/Reports/pages";
import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { OrgUserRole } from "globals";
import { FeatureFlagName } from "hooks/useFeatureFlags";
import { MainLayout } from "layouts";
import {
  ChangePassword,
  ChatSection,
  ContactUs,
  MyProfile,
  Notifications,
  QuotaUtilisation,
  Settings,
  WordCountPayment,
  WordCountPaymentStatus,
} from "pages";
import { Route } from "react-router-dom";
import { AdminRoutes } from "./AdminRoutes";
import { ROUTE_PATH } from "./routePath";

interface PrivateRoutesProps {
  userRole: OrgUserRole;
  isOrganisationEnabled: boolean;
  IsOfflineAccount: boolean;
  isFeatureEnabled: (feature: FeatureFlagName) => boolean;
}

const PrivateRoutes = ({
  userRole,
  isOrganisationEnabled,
  IsOfflineAccount,
  isFeatureEnabled,
}: PrivateRoutesProps) => {
  return (
    <Route element={<MainLayout />}>
      <Route path={ROUTE_PATH.HOME} element={<ChatSection />} />
      <Route path={`${ROUTE_PATH.HOME}/:id`} element={<ChatSection />} />
      <Route path={ROUTE_PATH.SETTINGS} element={<Settings />} />
      <Route path={ROUTE_PATH.MY_PROFILE} element={<MyProfile />} />
      <Route path={ROUTE_PATH.CHANGE_PASSWORD} element={<ChangePassword />} />
      <Route path={ROUTE_PATH.NOTIFICATIONS} element={<Notifications />} />
      <Route path={ROUTE_PATH.CONTACT_US} element={<ContactUs />} />
      <Route
        path={ROUTE_PATH.WORD_COUNT_PAYMENT}
        element={<WordCountPayment />}
      />
      <Route
        path={ROUTE_PATH.WORD_COUNT_PAYMENT_STATUS}
        element={<WordCountPaymentStatus />}
      />
      {/* <Route path={ROUTE_PATH.SUGGEST_FEATURE} element={<SuggestFeature />} />
      <Route path={ROUTE_PATH.REPORT_BUG} element={<ReportBug />} /> */}
      <Route
        path={ROUTE_PATH.QUOTA_UTILISATION}
        element={<QuotaUtilisation />}
      />
      {userRole !== OrgUserRole.USER &&
        AdminRoutes({ isOrganisationEnabled, IsOfflineAccount })}

      {isFeatureEnabled("REPORTS") && (
        <Route>
          <Route path={REPORTS_ROUTE_PATH.REPORTS} element={<REPORTS />} />
          <Route
            path={REPORTS_ROUTE_PATH.GENERATE_REPORT}
            element={<GenerateReport />}
          />
          <Route
            path={REPORTS_ROUTE_PATH.REPORT_DETAILS}
            element={<REPORTS />}
          />
        </Route>
      )}
    </Route>
  );
};

export default PrivateRoutes;
