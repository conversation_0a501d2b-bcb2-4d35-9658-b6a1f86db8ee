import { RiCloseLine } from "@remixicon/react";
import { useDeleteExportLogo } from "api";
import { ImageCropperModal } from "components";
import { useImageCropper } from "hooks/useImageCropper";
import { <PERSON><PERSON>, Card, Form } from "react-bootstrap";
import toast from "react-hot-toast";
import { setConfirmModalConfig, setOrganisation } from "stores";
import { isValidFileSize, isValidFileType } from "utils";
import LogoPicture from "../LogoPicture";
import { useRef } from "react";

const PRIMARY_LOGO_MIN_HEIGHT = 200;
const SECONDARY_LOGO_MIN_HEIGHT = 200;

export default function LogoSelection({
  values,
  setFieldValue,
  orgId,
  organisation,
}: any) {
  const {
    showCropModal,
    setShowCropModal,
    setCrop,
    config,
    imgRef,
    setCompletedCrop,
    imgSrc,
    openCropper,
    saveCroppedImage,
    fieldKey,
  } = useImageCropper();
  const { crop, scale, rotate, aspect, setScale } = config || {};
  const isCircularCrop = fieldKey === "primary_logo";
  const { mutateAsync: deleteExportLogo } = useDeleteExportLogo();
  const primaryLogoRef = useRef<any>(null);
  const secondaryLogoRef = useRef<any>(null);

  const handleLogoChange = (name: string, value: any) => {
    if (!isValidFileType(value) || !isValidFileSize(value)) {
      return;
    }

    openCropper(name, value);
  };

  const handleSaveCroppedImage = () => {
    saveCroppedImage((fieldName, croppedFile) => {
      setFieldValue(fieldName, croppedFile);
    }, isCircularCrop);
  };

  const handleRemoveLogo = async (name: string) => {
    try {
      const result: any = await deleteExportLogo({
        orgId,
      });
      if (result?.success) {
        toast.success(result?.message);
        setFieldValue(name, null);

        if (orgId) {
          setOrganisation({
            ...organisation,
            export_settings: {
              ...organisation?.export_settings,
              [name]: null,
            },
          });
        }
      }
    } catch (err: any) {
      console.log(err);
    }
  };

  const onClickRemove = async (name: string) => {
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleRemoveLogo(name),
        content: {
          heading: "Delete Logo",
          description: "Are you sure you want to delete this logo?",
        },
        buttonText: "Delete",
      },
    });
  };

  const closeCropModal = () => {
    setShowCropModal(false);
    setScale(1);
    if (fieldKey === "primary_logo") {
      if (!primaryLogoRef.current) return;
      primaryLogoRef.current = null;
    } else if (fieldKey === "secondary_logo") {
      if (!secondaryLogoRef.current) return;
      secondaryLogoRef.current = null;
    }
  };

  return (
    <div className="logo-selection">
      <div className="d-flex flex-column flex-lg-row gap-3">
        <Card className="logo-card primary">
          <Card.Body className="d-flex flex-column text-center">
            <LogoPicture
              logo={values.primary_logo}
              imageProps={{ width: 100, height: 100 }}
              name="primary_logo"
              setFieldValue={handleLogoChange}
              imgRef={primaryLogoRef}
            />
            <Form.Label className="mt-3 fw-bold fs-5">
              Primary Logo
              <span className="fs-6 text-muted d-block">Min Height: 200px</span>
            </Form.Label>
          </Card.Body>
        </Card>
        <Card className="logo-card secondary position-relative">
          {values.secondary_logo && (
            <Button
              className="position-absolute top-0 end-0 bg-danger border-0"
              onClick={() => onClickRemove("secondary_logo")}
            >
              <RiCloseLine />
            </Button>
          )}
          <Card.Body className="d-flex flex-column text-center">
            <LogoPicture
              logo={values.secondary_logo}
              imageProps={{
                className: "object-fit-contain",
                width: "100%",
                minWidth: 100,
                maxWidth: 200,
                height: 100,
              }}
              name="secondary_logo"
              setFieldValue={handleLogoChange}
              imgRef={secondaryLogoRef}
            />
            <Form.Label className="mt-3 fw-bold fs-5 text-center">
              Secondary Logo
              <span className="fs-6 text-muted d-block">(Company Logo)</span>
              <span className="fs-6 text-muted d-block">
                Min Height: {SECONDARY_LOGO_MIN_HEIGHT}px
              </span>
            </Form.Label>
          </Card.Body>
        </Card>
      </div>

      <ImageCropperModal
        show={showCropModal}
        onClose={closeCropModal}
        cropperConfig={{
          imgSrc,
          crop,
          setCrop,
          setCompletedCrop,
          aspect: isCircularCrop ? aspect : undefined,
          imgRef,
          scale,
          setScale,
          rotate,
          isCircularCrop,
          ...(fieldKey === "primary_logo"
            ? { minHeight: PRIMARY_LOGO_MIN_HEIGHT }
            : { minHeight: 200 }),
          enableScale: !isCircularCrop,
        }}
        onSave={handleSaveCroppedImage}
      />
    </div>
  );
}
