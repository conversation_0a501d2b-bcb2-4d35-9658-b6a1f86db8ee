import { useCallback, useState } from "react";
import { isValidDocType } from "utils";

interface UsePasteFileReturn {
  isPasting: boolean;
  onPaste: (e: React.ClipboardEvent) => void;
}

const usePasteFile = (
  onFilePaste: (file: File) => void,
): UsePasteFileReturn => {
  const [isPasting, setIsPasting] = useState(false);

  const onPaste = useCallback(
    (e: React.ClipboardEvent) => {
      const items = e.clipboardData.items;
      if (!items) return;

      const files = Array.from(items)
        .filter((item) => item.kind === "file")
        .map((item) => item.getAsFile())
        .filter((file): file is File => file !== null);

      if (files.length > 0) {
        e.preventDefault();
        setIsPasting(true);

        if (isValidDocType(files[0])) {
          onFilePaste(files[0]);
        }

        setTimeout(() => setIsPasting(false), 100);
      }
    },
    [onFilePaste],
  );

  return { isPasting, onPaste };
};

export default usePasteFile;
