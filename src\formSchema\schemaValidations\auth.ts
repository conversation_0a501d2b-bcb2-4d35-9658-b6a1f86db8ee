import * as Yup from "yup";
import {
  confirmPasswordValidation,
  emailValidation,
  passwordValidation,
  stringRequiredValidation,
} from "./common";

// Validation Schema
export const LoginValidations = Yup.object().shape({
  email: emailValidation,
  password: stringRequiredValidation("Password"),
});

export const SignupValidations = Yup.object().shape({
  email: emailValidation,
  password: passwordValidation("Password"),
  full_name: stringRequiredValidation("Full Name").max(
    30,
    "Full Name must be less than 30 characters.",
  ),
  confirmPassword: confirmPasswordValidation("password"),
  agreeTerms: Yup.boolean().oneOf([true], "You must agree to the terms."),
});

export const ForgotPasswordValidations = Yup.object().shape({
  email: emailValidation,
});

export const ResetPasswordValidations = Yup.object().shape({
  password: passwordValidation("Password"),
  confirmPassword: confirmPasswordValidation("password"),
});

export const ChangePasswordValidations = Yup.object().shape({
  currentPassword: passwordValidation("Current Password"),
  newPassword: passwordValidation("New Password").notOneOf(
    [Yup.ref("currentPassword")],
    "New password must be different from current password.",
  ),
  confirmPassword: confirmPasswordValidation("newPassword"),
});

export const ContactUsValidations = Yup.object().shape({
  full_name: stringRequiredValidation("Full Name").max(
    30,
    "Full Name must be less than 30 characters.",
  ),
  email: emailValidation,
  description: stringRequiredValidation("Description")
    .min(10, "Description must be at least 10 characters long")
    .max(500, "Description cannot be longer than 500 characters"),
});

export const DocSettingValidation = Yup.object().shape({
  font: stringRequiredValidation("Font"),
  primary_logo: Yup.mixed().required("Primary Logo is required"),
});
