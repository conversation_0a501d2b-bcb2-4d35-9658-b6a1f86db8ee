import { <PERSON>zyLoadWrapper } from "components";
import { AuthLayout } from "layouts";
import { lazy } from "react";
import { Route } from "react-router-dom";
import { ROUTE_PATH } from "./routePath";

const Login = lazy(() => import("pages/Auth/Login"));
const Signup = lazy(() => import("pages/Auth/Signup"));
const ForgotPassword = lazy(() => import("pages/Auth/Password/ForgotPassword"));
const ResetPassword = lazy(() => import("pages/Auth/Password/ResetPassword"));
const OTPVerify = lazy(() => import("pages/Auth/OTPVerify"));
const AuthSuccess = lazy(() => import("pages/Auth/AuthSuccess"));

const PUBLIC_ROUTES = [
  { path: ROUTE_PATH.LOGIN, element: Login },
  { path: ROUTE_PATH.SIGNUP, element: Signup },
  { path: ROUTE_PATH.FORGOT, element: ForgotPassword },
  { path: ROUTE_PATH.RESET, element: ResetPassword },
  { path: ROUTE_PATH.OTP, element: OTPVerify },
  {
    path: ROUTE_PATH.OTP_SUCCESS,
    element: AuthSuccess,
    extraProps: { type: "VERIFY_ACCOUNT" },
  },
  {
    path: ROUTE_PATH.RESET_SUCCESS,
    element: AuthSuccess,
    extraProps: { type: "RESET_PASSWORD" },
  },
];

const PublicRoutes = () => {
  return (
    <Route element={<AuthLayout />}>
      {PUBLIC_ROUTES.map(({ path, element: Component, extraProps }) => (
        <Route
          key={path}
          path={path}
          element={<LazyLoadWrapper Component={Component} {...extraProps} />}
        />
      ))}
    </Route>
  );
};

export default PublicRoutes;
