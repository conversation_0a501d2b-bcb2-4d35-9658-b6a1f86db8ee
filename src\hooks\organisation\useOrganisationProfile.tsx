import {
  useCreateOrganisation,
  useGetOrganisation,
  useUpdateOrganisation,
} from "api";
import { useFormik } from "formik";
import { OrganisationProfileValidations } from "formSchema/schemaValidations";
import { DEFAULT_ORG_PROMPTS } from "globals";
import { useUpdateOrgPrompt } from "hooks";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { setDocSettings } from "stores";
import { getOrganisationInfo, setOrganisation } from "stores/user";
import useUtilStore, { setTempOrgProfile } from "stores/util";
import { PromptInterface } from "types";
import { convertObjToFormData } from "utils";

interface OrgConfig {
  show_prompt: boolean;
  export_settings: boolean;
}

const ORGANISATION_SETTINGS: any = {
  basic_filters: {
    key: "basic_filters",
    label: "Require at least Basic Privacy Filters",
  },
  all_filters: {
    key: "all_filters",
    label: "Require all Privacy Filters",
  },
  show_logo: {
    key: "show_logo",
    label: "Display Company Profile Picture",
  },
  delete_chat_history: {
    key: "delete_chat_history",
    label: "Disable Conversation History Delete Function",
  },
  export_chat_history: {
    key: "export_chat_history",
    label: "Disable Conversation History Export Function",
  },
  disable_default_prompts: {
    key: "disable_default_prompts",
    label: "Disable Default Prompts",
  },
};

export const useOrganisationProfile = () => {
  const organisation = getOrganisationInfo();
  const orgDocSettings = useUtilStore((state) => state.docSettings);
  const tempOrgProfile: any = useUtilStore((state) => state.tempOrgProfile);

  const { data: { organization: orgInfo = {} } = {} } = useGetOrganisation({
    staleTime: 0,
  });
  const { mutateAsync: createOrganisation } = useCreateOrganisation();
  const { mutateAsync: updateOrganisation } = useUpdateOrganisation();
  const navigate = useNavigate();
  const [orgConfig, setOrgConfig] = useState<OrgConfig>({
    show_prompt: false,
    export_settings: false,
  });
  const [orgPrompts, setOrgPrompts] = useState<any>([]);

  const { onClickReset, onClickSubmit } = useUpdateOrgPrompt();

  const formatPromptsPayload = (prompts: any) => {
    return prompts.map((prompt: any) => ({
      prompt: prompt.prompt,
      title: prompt.title,
      icon: prompt.icon,
    }));
  };

  const formatPromptsTemplates = (prompts: any) => {
    return prompts.map((prompt: any) => prompt?.file);
  };

  const handleSubmit = async (
    values: any,
    { setSubmitting, resetForm }: any,
  ) => {
    try {
      setSubmitting(true);
      const payload = {
        ...values,
        settings: JSON.stringify({ ...values.settings, ...orgConfig }),
      };

      const response: any = organisation?.id
        ? await updateOrganisation({
            id: organisation?.id,
            payload: convertObjToFormData(payload),
          })
        : await createOrganisation(
            convertObjToFormData({
              ...payload,
              prompts: JSON.stringify(formatPromptsPayload(orgPrompts)),
              templates: formatPromptsTemplates(orgPrompts) || [],
              font: orgDocSettings?.font || "",
              primary_logo: orgDocSettings?.primary_logo || "",
              secondary_logo: orgDocSettings?.secondary_logo || "",
            }),
          );
      if (response?.success) {
        toast.success(response?.message);
        resetForm();
        setOrgPrompts([]);
        setDocSettings({});
        navigate(ROUTE_PATH.ORGANISATION_MANAGER);
      }
    } catch (error: any) {
      console.error(error?.response?.data?.message);
    } finally {
      setSubmitting(false);
    }
  };

  const formik: any = useFormik({
    initialValues: {
      title: organisation?.title || "",
      logo: organisation?.logo || undefined,
      settings: Object.keys(ORGANISATION_SETTINGS).reduce(
        (acc, key: any) => ({
          ...acc,
          [key]: organisation?.settings?.[key] || false,
        }),
        {},
      ),
      prompts: organisation?.prompts?.length ? organisation?.prompts : [],
    },
    validationSchema: OrganisationProfileValidations,
    enableReinitialize: true,
    onSubmit: handleSubmit,
  });

  const handleSwitchChange = ({ key, checked, formik }: any) => {
    formik.setFieldValue(`settings.${key}`, checked);

    if (key === "all_filters" && checked) {
      formik.setFieldValue(`settings.basic_filters`, true);
    }
  };

  const onSavePrompt = (prompt_data: PromptInterface) => {
    if (organisation?.id) {
      onClickSubmit({ organisation, prompt_data });
      return;
    }
    const { prompt_id } = prompt_data;
    if (prompt_id === undefined) return;

    setOrgPrompts((prev: any) => {
      if (!Array.isArray(prev)) return [prompt_data];

      const updatedPrompts = [...prev];

      if (prompt_id >= updatedPrompts.length) {
        updatedPrompts.push(prompt_data);
      } else {
        updatedPrompts[prompt_id] = prompt_data;
      }

      return updatedPrompts;
    });
  };

  const onResetPrompt = (data: any) => {
    onClickReset({ ...data, organisation });
  };

  const getLocalPrompts = () => {
    return DEFAULT_ORG_PROMPTS.map((item: any) => {
      const storedPrompt = orgPrompts.find(
        (prompt: any) => prompt.prompt_id === item.prompt_id,
      );
      return storedPrompt || item;
    });
  };

  const onLocalReset = (prompt_id: any) => {
    if (prompt_id === undefined) return;
    setOrgPrompts((prev: any) =>
      prev.filter((item: any) => item.prompt_id !== prompt_id),
    );
  };

  const handleSaveDraft = () => {
    setTempOrgProfile({
      formData: formik.values,
      orgConfig,
      orgPrompts,
    });
  };

  useEffect(() => {
    setOrgConfig({
      show_prompt: organisation?.settings?.show_prompt,
      export_settings: organisation?.settings?.export_settings,
    });
  }, [
    organisation?.settings?.show_prompt,
    organisation?.settings?.export_settings,
  ]);

  useEffect(() => {
    if (orgInfo?.id) {
      setOrganisation(orgInfo);
    }
  }, [orgInfo]);

  useEffect(() => {
    if (tempOrgProfile && tempOrgProfile?.formData) {
      formik.setValues(tempOrgProfile.formData);
      setOrgConfig(tempOrgProfile.orgConfig);
      setOrgPrompts(tempOrgProfile.orgPrompts);
    }
  }, [tempOrgProfile]);

  return {
    formik,
    organisation,
    orgConfig,
    setOrgConfig,
    onSavePrompt,
    onResetPrompt,
    onLocalReset,
    getLocalPrompts,
    handleSwitchChange,
    ORGANISATION_SETTINGS,
    navigate,
    handleSaveDraft,
  };
};
