@use "/src/styles/mixins/mixins.scss";

.report-builder-section {
  .header {
    padding-bottom: 0.5rem;
    &-title {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      background: #f9f9f9;
      border: 1px solid #ccc;
      padding-right: 2.5rem;
      border-radius: 6px;

      .report-title-input {
        flex: 1;
        padding: 0.75rem 2.5rem 0.75rem 1rem;
        border: none;
        background: transparent;
        font-size: 1rem;
        outline: none;
      }

      .edit-title-btn {
        position: absolute;
        right: 0.75rem;
        background: none;
        border: none;
        cursor: pointer;
        padding: 0;
        display: flex;
        align-items: center;
        height: 100%;

        svg {
          width: 1.2rem;
          height: 1.2rem;
        }
      }
    }

    &-actions {
      .action-btn {
        background-color: #0d3149;
        border-color: #0d3149;
        color: white;
        width: 45px !important;
        height: 45px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0;

        svg {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}
