@use "/src/styles/mixins/mixins.scss" as mixins;
@use "/src/assets/fonts/Urbanist/fontUrbanist.scss";

*,
::after,
::before {
  @include mixins.page-reset;
}

body {
  @include mixins.body-formatting;
  @include mixins.slim-scrollbar;
}

input {
  @include mixins.input-autofill-spin($include-spin-button: false);

  &[type="number"] {
    @include mixins.input-autofill-spin;
  }
}

.offcanvas-backdrop {
  background-color: #0d3149;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

button,
a {
  box-shadow: none !important;
  outline: none !important;
}

.table-responsive {
  @include mixins.slim-scrollbar;
}

.toaster-wrapper > div {
  display: none !important;
}

.toaster-wrapper > div:first-child {
  display: flex !important;
}

input[type="password"]::-ms-reveal,
input[type="password"]::-ms-clear,
input[type="password"]::-webkit-textfield-decoration-container {
  display: none;
}

input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
  appearance: none;
}

input[type="date"] {
  color: #70828d;

  &.value-added {
    color: #0d3149;
  }

  &::-webkit-calendar-picker-indicator {
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    transform: translateY(-50%);
    right: 28px;
    z-index: 1;
    opacity: 0;
  }

  & + img {
    width: 20px;
    height: 20px;
    right: 30px;
  }
}
