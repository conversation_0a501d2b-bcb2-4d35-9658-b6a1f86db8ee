import { Mark, mergeAttributes } from "@tiptap/core";

export interface ContentLockOptions {
  HTMLAttributes: Record<string, any>;
}

declare module "@tiptap/core" {
  interface Commands<ReturnType> {
    contentLock: {
      /**
       * Set content lock
       */
      setContentLock: () => ReturnType;
      /**
       * Toggle content lock
       */
      toggleContentLock: () => ReturnType;
      /**
       * Unset content lock
       */
      unsetContentLock: () => ReturnType;
    };
  }
}

export const ContentLock = Mark.create<ContentLockOptions>({
  name: "contentLock",

  addOptions() {
    return {
      HTMLAttributes: {},
    };
  },

  addAttributes() {
    return {
      locked: {
        default: true,
        parseHTML: (element) => element.getAttribute("data-locked") === "true",
        renderHTML: (attributes) => {
          if (!attributes.locked) {
            return {};
          }
          return {
            "data-locked": attributes.locked,
          };
        },
      },
    };
  },

  parseHTML() {
    return [
      {
        tag: 'span[data-locked="true"]',
      },
    ];
  },

  renderHTML({ HTMLAttributes }) {
    return [
      "span",
      mergeAttributes(
        {
          "data-locked": "true",
          class: "content-locked",
        },
        this.options.HTMLAttributes,
        HTMLAttributes,
      ),
      0,
    ];
  },

  addCommands() {
    return {
      setContentLock:
        () =>
        ({ commands }) => {
          return commands.setMark(this.name, { locked: true });
        },
      toggleContentLock:
        () =>
        ({ commands }) => {
          return commands.toggleMark(this.name, { locked: true });
        },
      unsetContentLock:
        () =>
        ({ commands }) => {
          return commands.unsetMark(this.name);
        },
    };
  },

  addKeyboardShortcuts() {
    return {
      "Mod-Shift-l": () => this.editor.commands.toggleContentLock(),
    };
  },
});

export default ContentLock;
